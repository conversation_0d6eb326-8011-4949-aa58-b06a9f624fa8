import {
  Text,
  Field,
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  LinkField,
  RichText,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import Button from 'components/Elements/Button/Button';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useAppSelector } from 'src/stores/store';

type TransferMultiMeterProps = ComponentProps & {
  fields: {
    Title: Field<string>;
    Description: Field<string>;
    GotoSummaryText: Field<string>;
    GotoSummaryLink: LinkField;
  };
};

const TransferMultiMeter = (props: TransferMultiMeterProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let multiMeter = undefined;
  if (!isPageEditing) {
    multiMeter = useAppSelector((state) => state.transfer?.multiMeter);
  }
  const router = useRouter();
  return (
    <div className='sm:ml-[300px]'>
      <div className="max-w-830 m-auto my-[40px] mx-4 gap-4 flex flex-wrap">
        <h1 className="text-plus1 font-primaryBlack text-textPrimary sm:text-plus2">
          <Text field={props.fields.Title} />
        </h1>
        <p className="text-minus1 text-textQuattuordenary">
          <RichText field={props.fields.Description} />
        </p>
        <div>
          {multiMeter?.esiids.map((address) => (
            // eslint-disable-next-line react/jsx-key
            <>
              <p className="text-minus1 text-textQuattuordenary py-1">{address}</p>
            </>
          ))}
        </div>
      </div>
      <Button
        className="mx-4"
        onClick={() => router.push({ pathname: props.fields.GotoSummaryLink.value.href })}
      >
        {props.fields.GotoSummaryText.value}
      </Button>
    </div>
  );
};

export { TransferMultiMeter };
const Component = withDatasourceCheck()<TransferMultiMeterProps>(TransferMultiMeter);
export default aiLogger(Component, Component.name);
