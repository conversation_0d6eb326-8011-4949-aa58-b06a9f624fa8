const { themeVariants } = require('tailwindcss-theme-variants');

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  darkMode: 'class',
  safelist: [
    'bg-cactus',
    'bg-breeze',
    'bg-txulightblue',
    'bg-txuorange',
    'bg-txunavyblue',
    'bg-brown',
    'bg-charcoal-25',
    'pt-[30px]',
    'py-[20px]',
    'pl-[40px]',
    'my-[10px]',
    'ml-[30px]',
    'left-[10px]',
    'marker:text-[#0075DA]',
    'tee:marker:text-[#001E29]',
    'wide:w-[384px]',
    'gap-20',
    'marker:text-plus2',
    'list-inside',
    'list-disc',
    'ml-[15px]',
    'mt-[10px]',
    'gap-2',
    'grid',
    'py-4',
    'text-base',
    'sm:text-plus3',
    'text-tee-darkblue',
    'font-gProBold',
    'text-minus2',
    'sm:text-minus1',
    'text-tee-txtgrey',
    'font-gProRegular',
    'bg-primary-plan',
    'bg-secondary-plan',
    'bg-tertiary-plan',
    'sm:flex-row',
  ],
  theme: {
    extend: {
      colors: {
        // text-color
        textPrimary: '#326295', //tee-darkblue  //tee-hyperblue
        textSecondary: '#002554', //tee-primary
        textTertiary: '#BBC7D6', //tee-blue
        textQuaternary: '#D7D2CB',
        textQuinary: '#ffffff', //white
        textSenary: '#87858E', //tee-mediumgrey
        textSeptenary: '#5F5D68', //tee-darkgrey
        textOctonary: '#FFE25B', //tee-yellow
        textNonary: '#D7DF23', //tee-green
        textDenary: '#D8333F', //error
        textUndenary: '#353535',
        textDuodenary: '#727676',
        textTredenary: '#0093D0',
        textQuattuordenary: '#383543', //tee-txtgray
        textQuindenary: '#637A87',
        textSexdenary: '#188464', //Successgreen
        textSeptendenary: '#d7d6d9',
        textOctodenary: '#f3f2f3',
        textNovemdenary: '#f3f3f3',
        textVigintiunary: '#FCBC00',
        textVigintiduonary: '#78278B',

        // background
        bgPrimary: '#002554',
        bgSecondary: '#326295',
        bgTertiary: '#BBC7D6',
        bgQuaternary: '#8DC63F',
        bgQuinary: '#ffffff', //white
        bgSenary: '#87858E',
        bgSeptenary: '#5F5D68',
        bgOctonary: '#FFE25B',
        bgNonary: '#D7DF23',
        bgDenary: '#AC0040',
        bgUndenary: '#353535',
        bgDuodenary: '#727676',
        bgTredenary: '#0093D0',
        bgQuattuordenary: '#383543',
        bgQuindenary: '#637A87',
        bgSexdenary: '#188464',
        bgSeptendenary: '#d7d6d9',
        bgOctodenary: '#f3f2f3',
        bgNovemdenary: '#f3f3f3',
        bgVigintiunary: '#a77c29',
        'primary-plan': '#78268b',
        'secondary-plan': '#a26aaf',
        'tertiary-plan': '#6fbed9',

        // border color
        borderPrimary: '#002554',
        borderSecondary: '#326295',
        borderTertiary: '#BBC7D6',
        borderQuaternary: '#326295',
        borderQuinary: '#ffffff',
        borderSenary: '#87858E',
        borderSeptenary: '#5F5D68',
        borderOctonary: '#FFE25B',
        borderNonary: '#D7DF23',
        borderDenary: '#AC0040',
        borderUndenary: '#353535',
        borderDuodenary: '#727676',
        borderTredenary: '#0093D0',
        borderQuattuordenary: '#383543',
        borderQuindenary: '#637A87',
        borderSexdenary: '#188464',
        borderSeptendenary: '#d7d6d9',
        borderOctodenary: '#f3f2f3',
        borderVigintiduonary: '#326295',
        borderVigintiternary: '#326295',
        borderNovemdenary: '',

        // hover color
        hoverPrimary: '#002554',
        hoverSecondary: '#326295',
        hoverTertiary: '#BBC7D6',
        hoverQuaternary: '#D7D2CB',
        hoverQuinary: '#ffffff',
        hoverSenary: '#87858E',
        hoverSeptenary: '#5F5D68',
        hoverOctonary: '#FFE25B',
        hoverNonary: '#D7DF23',
        hoverDenary: '#AC0040',
        hoverUndenary: '#353535',
        hoverDuodenary: '#727676',
        hoverTredenary: '#0093D0',
        hoverQuattuordenary: '#383543',
        hoverQuindenary: '#637A87',
        hoverSexdenary: '#188464',
        hoverSeptendenary: '#d7d6d9',
        hoverOctodenary: '#f3f2f3',
        hoverNovemdenary: '',

        buttonPrimary: '#002554',
        buttonSecondary: '#326295',
        buttonTertiary: '#BBC7D6',
        buttonQuaternary: '#D7D2CB',
        buttonQuinary: '#ffffff',
        buttonSenary: '#87858E',
        buttonSeptenary: '#5F5D68',
        buttonOctonary: '#FFE25B',
        buttonNonary: '#D7DF23',
        buttonDenary: '#AC0040',
        buttonUndenary: '#353535',
        buttonDuodenary: '#727676',
        buttonTredenary: '#0093D0',
        buttonQuattuordenary: '#383543',
        buttonQuindenary: '#637A87',
        buttonSexdenary: '#188464',
        buttonSeptendenary: '#d7d6d9',
        buttonOctodenary: '#f3f2f3',
        buttonNovemdenary: '',

        bgTooltip: '#002554',
        bgFooter: '#d7d6d9',
        textFooter: '#383543',
        textzipcode: '#326295',
      },
      fontSize: {
        minus4: ['10px', '16px'],
        minus3: ['12px', '18px'],
        minus2: ['14px', '22px'],
        minus1: ['16px', '26px'],
        base: ['18px', '24px'],
        plus1: ['20px', '26px'],
        plus2: ['24px', '30px'],
        plus3: ['32px', '38px'],
        plus4: ['40px', '46px'],
        plus5: ['56px', '62px'],
        plus6: ['64px', '68px'],
        plus7: ['72px', '76px'],
      },
      letterSpacing: {
        tighter: '-1.6px',
      },
      borderWidth: {
        3: '3px',
      },
      fontFamily: {
        primaryRegular: ['OpenSans-Regular'],
        primaryBold: ['OpenSans-Bold'],
        primaryserratBold: ['Montserrat-Bold'],
        //Todo need to update actual font family
        primaryMedium: ['OpenSans-Regular'],
        primaryBlack: ['OpenSans-Bold'],
        primarySemiBold: ['OpenSans-Bold'],
      },
      boxShadow: {
        'grey-shadow': '0px 4px 35px -6px #d6d6d6',
        'grey-shadow1': '0px 0px 15px 3px #dadada',
        'grey-shadow2': '0px 0px 20px 0px #5c5c5c',
        '3xl': '0px 8px 32px -8px rgba(63,71,90,0.4)',
      },
      borderRadius: {
        '5px': '5px',
        '10px': '10px',
        '15px': '15px',
        '20px': '20px',
        '100px': '100px',
      },
      spacing: {
        '0px': '0px',
        '3px': '3px',
        '5px': '5px',
        '10px': '10px',
        '15px': '15px',
        '16px': '16px',
        '18px': '18px',
        '20px': '20px',
        '25px': '25px',
        '30px': '30px',
        '33px': '33px',
        '40px': '40px',
        '50px': '50px',
        '60px': '60px',
        '75px': '75px',
        '150px': '150px',
        minus6px: '-6px',
        '-100px': '-100px',
        '15p': '15%',
        '13p': '13%',
        '8p': '-8%',
        '5p': '5%',
        minus50p: '-50px',
      },
      minHeight: {
        60: '60px',
        122: '122px',
        76: '76px',
      },
      maxHeight: {
        40: '40px',
      },
      height: {
        '5p': '5px',
        '40p': '40%',
      },
      maxWidth: {
        1216: '1216px',
        1440: '1440px',
        1250: '1250px',
        1256: '1256px',
        1240: '1240px',
        1008: '1008px',
        312: '312px',
        320: '320px',
        904: '904px',
        145: '145px',
        592: '592px',
        610: '610px',
        343: '343px',
        644: '644px',
        400: '400px',
        350: '350px',
        744: '744px',
        864: '864px',
        800: '800px',
        830: '830px',
        488: '488px',
        280: '280px',
        220: '220px',
        271: '271px',
        176: '176px',
        120: '120px',
        150: '150px',
        25: '25px',
        84: '84px',
        90: '90px',
        '10p': '10%',
        '14p': '14%',
        '20p': '20%',
        '24p': '24%',
        '23p': '23%',
        '20p': '20%',
        '19p': '19%',
        '33p': '33.3333%',
        '70p': '70%',
        '50p': '50%',
        '60p': '60%',
      },
      minWidth: {
        268: '268px',
        176: '176px',
        281: '281px',
        100: '100%',
      },
      lineHeight: {
        'h-14px': '14px',
        'h-18px': '18px',
        'h-20px': '20px',
        'h-38px': '38px',
        'h-56px': '56px',
      },
      flexBasis: {
        '10p': '10%',
        '20p': '20%',
        '30p': '30%',
        '40p': '40%',
        '60p': '60%',
      },
      flex: {
        '0-0-auto': '0 0 auto',
        '10p': '0 0 10%',
        '14p': '0 0 14%',
        '24p': '0 0 24%',
        '20p': '0 0 20%',
        '23p': '0 0 23%',
        '30p': '0 0 30%',
        '20p': '0 0 20%',
        '19p': '0 0 19%',
        '50p': '0 0 50%',
        '70p': '0 0 70%',
        '100p': '0 0 100%',
      },
      zIndex: {
        999: '99999',
        999: '999',
        99: '99',
        9: '9',
      },
      screens: {
        '4xl': '1920px',
        wide: {
          raw: `only screen and (max-height: 420px) and (max-width: 960px)`,
          raw: `only screen and (max-height: 1180px) and (max-width: 820px)`,
          raw: `only screen and (max-height: 820px) and (max-width: 1180px)`,
        },
        ipad: {
          raw: `only screen and (max-height: 1180px) and (max-width: 820px)`,
          raw: `only screen and (max-width: 820px) and (min-width: 760px)`,
        },
      },
    },
  },
};
