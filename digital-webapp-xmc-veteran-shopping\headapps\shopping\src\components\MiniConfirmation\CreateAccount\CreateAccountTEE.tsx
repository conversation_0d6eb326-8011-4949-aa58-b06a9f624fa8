import { Checkbox, Select as MantineSelect, PasswordInput, TextInput } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import {
  Field,
  RichText,
  Text,
  withDatasourceCheck,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { COAFormValues } from '../MiniConfirmationContainer/MiniConfirmationContainerTEE';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown } from '@fortawesome/pro-light-svg-icons';
import { faEyeSlash, faEye } from '@fortawesome/pro-regular-svg-icons';
import { faCircleCheck } from '@fortawesome/pro-solid-svg-icons';
import { useAppSelector } from 'src/stores/store';
import { useEffect } from 'react';

export interface SecurityQuestionTypes {
  fields: {
    Title: Field<string>;
    Text: Field<string>;
  };
}

type CreateAccountTEEProps = ComponentProps & {
  fields: {
    data: {
      item: {
        CreateAccounTitleText: Field<string>;
        CreateAccountTagLine: Field<string>;
        MandatoryInfoText: Field<string>;
        EmailAddressLabel: Field<string>;
        EmailAddressHelperText: Field<string>;
        PasswordText: Field<string>;
        PasswordHelperText: Field<string>;
        ConfirmPasswordLabel: Field<string>;
        SecurityQuestionLabel: Field<string>;
        //SecurityQuestions: SecurityQuestionTypes[];
        SecurityQuestions: {
          targetItems: { Title: Field<string> }[];
        };
        SecurityAnswerLabel: Field<string>;
        AcceptanceCheckBoxText: Field<string>;
        BillingTitle: Field<string>;
        BillingConsentCheckBoxLabel: Field<string>;
        SubmitButtonLabel: Field<string>;
        SkipLabel: Field<string>;
      };
    };
  };
  form: UseFormReturnType<COAFormValues>;
};

const CreateAccountTEE = (props: CreateAccountTEEProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let customerInfo = undefined;
  if (!isPageEditing) {
    customerInfo = useAppSelector((state) => state.enrollment?.customerInfo);
  }
  useEffect(() => {
    if (customerInfo?.email) {
      props.form.setFieldValue('custUserName', customerInfo.email);
    }
  }, [customerInfo?.email]);

  function isValidEmail(email: string): boolean {
    const emailRegex = /^[A-Za-z0-9._%-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,4}$/;
    return emailRegex.test(email);
  }

  return (
    <div className="w-full sm:max-w-[894px]">
      <hr className="h-px my-8 bg-charcoal-25 border w-full m-auto wide:max-w-full ipad:max-w-full" />
      <div className="flex flex-col gap-3 sm:gap-6 max-w-full px-[15px] sm:px-0 m-auto wide:max-w-full ipad:max-w-full ipad:px-[40px]">
        <div className="sm:flex gap-4 items-center pt-4">
          <Text
            tag="p"
            // className="font-primaryBlack text-txublue text-plus1 -tracking-[0.25px] sm:text-plus3 sm:-tracking-[0.46px]"
            className="font-primaryBlack text-plus3 text-textQuattuordenary tracking-[0.25px] pb-[15px]"
            field={props?.fields?.data.item.CreateAccounTitleText}
          />
          <div className="text-textSenary text-minus3 items-center">
            {props?.fields?.data.item.MandatoryInfoText.value}
          </div>
        </div>
      </div>
      <div className="create-account-section rounded-xl border-0 border-textPrimary  p-4 h-fit w-full sm:p-0 sm:pt-0 relative wide:max-w-full ipad:max-w-full">
        <div className="flex flex-col gap-3 sm:gap-6">
          <Text
            tag="p"
            // className="font-primaryRegular text-minus2 text-txublue mt-2"
            className="font-primaryRegular  text-minus1  text-textQuattuordenary leading-[30px] tracking-normal"
            field={props?.fields?.data.item.CreateAccountTagLine}
          />
        </div>
        <div className="flex flex-col">
          <div className="flex flex-col gap-1 mt-[20px]">
            <Text
              tag="p"
              // className="font-primaryBold text-minus2 text-txublue mt-2"
              className="font-primaryBold  text-textQuattuordenary text-minus2   tracking-normal leading-[26px]"
              field={props?.fields?.data.item.EmailAddressLabel}
            />
            <TextInput
              {...props.form.getInputProps('custUserName')}
              className="font-primaryRegular  text-textQuattuordenary text-minus2   tracking-normal leading-[26px]"
              sx={{
                width: '100%',
                ['@media (min-width: 640px)']: {
                  width: '360px',
                },
              }}
              rightSection={
                <FontAwesomeIcon
                  icon={faCircleCheck}
                  className={`text-textSexdenary mr-[4px] text-plus2 ${
                    isValidEmail(props.form.values.custUserName) ? 'visible' : 'invisible'
                  }`}
                />
              }
            />
            {/* <Text
              tag="p"
              className="text-charcoal-full text-base tracking-normal leading-[26px] font-primaryRegular"
              field={{ value: customerInfo.email }}
            /> */}
          </div>
          <Text
            tag="p"
            // className="font-primaryRegular text-minus2 text-txublue mt-2"
            className="font-primaryRegular  text-textQuattuordenary text-minus2 tracking-normal"
            field={props?.fields?.data.item.EmailAddressHelperText}
          />
        </div>
        <div className="flex flex-col gap-5 sm:gap-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-5 sm:gap-8 sm:items-start mt-[24px]">
            <div>
              <PasswordInput
                sx={{
                  width: '100%',
                  ['@media (min-width: 767px)']: {
                    width: '100%',
                  },
                }}
                visibilityToggleIcon={({ reveal }) =>
                  reveal ? (
                    <FontAwesomeIcon
                      icon={faEyeSlash}
                      className="text-digitalBlueBonnet hover:text-textPrimary text-textSecondary "
                    />
                  ) : (
                    <FontAwesomeIcon
                      icon={faEye}
                      className="text-digitalBlueBonnet hover:text-textPrimary text-textSecondary "
                    />
                  )
                }
                className="font-primaryRegular text-[16px]  text-textQuattuordenary tracking-normal leading-[24px]"
                label={props?.fields?.data.item.PasswordText?.value}
                {...props.form.getInputProps('password')}
              />
              <Text
                tag="p"
                // className="font-primaryRegular text-minus1 text-charcoal-full"
                className="font-primaryRegular  text-minus2  text-textQuattuordenary tracking-normal wide:max-w-[300px] ipad:max-w-[300px]"
                field={props?.fields?.data.item.PasswordHelperText}
              />
            </div>
            <div>
              <PasswordInput
                sx={{
                  width: '100%',
                  ['@media (min-width: 767px)']: {
                    width: '100%',
                  },
                }}
                visibilityToggleIcon={({ reveal }) =>
                  reveal ? (
                    <FontAwesomeIcon
                      icon={faEyeSlash}
                      className="text-digitalBlueBonnet hover:text-textPrimary text-textSecondary "
                    />
                  ) : (
                    <FontAwesomeIcon
                      icon={faEye}
                      className="text-digitalBlueBonnet hover:text-textPrimary text-textSecondary "
                    />
                  )
                }
                className="font-primaryRegular  text-[16px]  text-textQuattuordenary tracking-normal leading-[24px]"
                label={props?.fields?.data.item.ConfirmPasswordLabel?.value}
                {...props.form.getInputProps('confirmpassword')}
              />
            </div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-5 sm:gap-8 sm:items-start mt-0">
            <div>
              <label className="font-primaryBold  text-[16px]  text-textQuattuordenary tracking-normal leading-[24px]">
                {props?.fields?.data.item.SecurityQuestionLabel?.value}
              </label>
              <MantineSelect
                className="font-primaryRegular  text-[16px]  text-textQuattuordenary tracking-normal leading-[24px]"
                sx={{
                  width: '100%',
                  ['@media (min-width: 767px)']: {
                    width: '100%',
                  },
                }}
                {...props.form.getInputProps('securityquestion')}
                data={props?.fields?.data.item.SecurityQuestions?.targetItems.map(
                  (securityquestion) => securityquestion?.Title?.value
                )}
                rightSection={
                  <FontAwesomeIcon
                    icon={faChevronDown}
                    className="text-digitalBlueBonnet hover:text-textPrimary text-textSecondary "
                  />
                }
              />
            </div>
            <div>
              <label className="font-primaryBold  text-[16px]  text-textQuattuordenary tracking-normal leading-[24px]">
                {props?.fields?.data.item.SecurityAnswerLabel?.value}
              </label>
              <TextInput
                className="font-primaryRegular  text-[16px] text-textQuattuordenary tracking-normal leading-[24px]"
                sx={{
                  width: '100%',
                  ['@media (min-width: 767px)']: {
                    width: '100%',
                  },
                }}
                {...props.form.getInputProps('securityanswer')}
              />
            </div>
          </div>
          <div className="flex flex-col gap-5 sm:flex-row sm:gap-8 sm:items-start">
            <Checkbox
              sx={{
                width: '100%',
                ['@media (min-width: 767px)']: {
                  width: '100%',
                },
              }}
              className="font-primaryRegular  text-[16px]  text-textQuattuordenary tracking-normal leading-[20px]"
              label={
                <RichText
                  className="font-primaryRegular  text-[16px]  text-textQuattuordenary  tracking-normal leading-[20px] inline-link"
                  field={{ value: props.fields.data.item.AcceptanceCheckBoxText.value }}
                />
              }
              {...props.form.getInputProps('acceptance')}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export { CreateAccountTEE };
const Component = withDatasourceCheck()<CreateAccountTEEProps>(CreateAccountTEE);
export default aiLogger(Component, Component.name);
