import { MantineThemeOverride } from '@mantine/core';
import { MantineTheme } from '@mantine/core';

export const mantine4ChangeTheme: MantineThemeOverride = {
  fontFamily: 'OpenSans-Regular',
  other: {
    colors: {
      // text-color
      textPrimary: '#002554', //tee-darkblue  //tee-hyperblue
      textSecondary: '#326295', //tee-primary
      textTertiary: '#BBC7D6', //tee-blue
      textQuaternary: '#D7D2CB',
      textQuinary: '#ffffff', //white
      textSenary: '#87858E', //tee-mediumgrey
      textSeptenary: '#5F5D68', //tee-darkgrey
      textOctonary: '#FFE25B', //tee-yellow
      textNonary: '#D7DF23', //tee-green
      textDenary: '#D8333F', //error
      textUndenary: '#353535',
      textDuodenary: '#727676',
      textTredenary: '#0093D0',
      textQuattuordenary: '#383543', //tee-txtgray
      textQuindenary: '#637A87',
      textSexdenary: '#188464', //Successgreen
      textSeptendenary: '#d7d6d9',
      textOctodenary: '#f3f2f3',
      textNovemdenary: '#f3f3f3',

      // background
      bgPrimary: '#002554',
      bgSecondary: '#326295',
      bgTertiary: '#BBC7D6',
      bgQuaternary: '#8DC63F',
      bgQuinary: '#ffffff', //white
      bgSenary: '#87858E',
      bgSeptenary: '#5F5D68',
      bgOctonary: '#FFE25B',
      bgNonary: '#D7DF23',
      bgDenary: '#AC0040',
      bgUndenary: '#353535',
      bgDuodenary: '#727676',
      bgTredenary: '#0093D0',
      bgQuattuordenary: '#383543',
      bgQuindenary: '#637A87',
      bgSexdenary: '#188464',
      bgSeptendenary: '#d7d6d9',
      bgOctodenary: '#f3f2f3',
      bgNovemdenary: '#f3f3f3',
      bgVigintiunary: '#FCBC00',
      'primary-plan': '#78268b',
      'secondary-plan': '#a26aaf',
      'tertiary-plan': '#6fbed9',

      // border color
      borderPrimary: '#002554',
      borderSecondary: '#326295',
      borderTertiary: '#BBC7D6',
      borderQuaternary: '#D7D2CB',
      borderQuinary: '#ffffff',
      borderSenary: '#87858E',
      borderSeptenary: '#5F5D68',
      borderOctonary: '#FFE25B',
      borderNonary: '#D7DF23',
      borderDenary: '#AC0040',
      borderUndenary: '#353535',
      borderDuodenary: '#727676',
      borderTredenary: '#0093D0',
      borderQuattuordenary: '#383543',
      borderQuindenary: '#637A87',
      borderSexdenary: '#188464',
      borderSeptendenary: '#d7d6d9',
      borderOctodenary: '#f3f2f3',
      borderVigintiternary: '#326295',
      borderNovemdenary: '',

      // hover color
      hoverPrimary: '#002554',
      hoverSecondary: '#326295',
      hoverTertiary: '#BBC7D6',
      hoverQuaternary: '#D7D2CB',
      hoverQuinary: '#ffffff',
      hoverSenary: '#87858E',
      hoverSeptenary: '#5F5D68',
      hoverOctonary: '#FFE25B',
      hoverNonary: '#D7DF23',
      hoverDenary: '#AC0040',
      hoverUndenary: '#353535',
      hoverDuodenary: '#727676',
      hoverTredenary: '#0093D0',
      hoverQuattuordenary: '#383543',
      hoverQuindenary: '#637A87',
      hoverSexdenary: '#188464',
      hoverSeptendenary: '#d7d6d9',
      hoverOctodenary: '#f3f2f3',
      hoverNovemdenary: '',

      buttonPrimary: '#002554',
      buttonSecondary: '#326295',
      buttonTertiary: '#BBC7D6',
      buttonQuaternary: '#D7D2CB',
      buttonQuinary: '#ffffff',
      buttonSenary: '#87858E',
      buttonSeptenary: '#5F5D68',
      buttonOctonary: '#FFE25B',
      buttonNonary: '#D7DF23',
      buttonDenary: '#AC0040',
      buttonUndenary: '#353535',
      buttonDuodenary: '#727676',
      buttonTredenary: '#0093D0',
      buttonQuattuordenary: '#383543',
      buttonQuindenary: '#637A87',
      buttonSexdenary: '#188464',
      buttonSeptendenary: '#d7d6d9',
      buttonOctodenary: '#f3f2f3',
      buttonNovemdenary: '',
    },
    fontFamily: {
      primaryRegular: ['OpenSans-Regular'],
      primaryBold: ['OpenSans-Bold'],
      primaryserratBold: ['Montserrat-Bold'],
      //Todo need to update actual font family
      primaryMedium: ['OpenSans-Regular'],
      primaryBlack: ['OpenSans-Bold'],
      primarySemiBold: ['OpenSans-Bold'],
    },
  },
  components: {
    Input: {
      styles: (theme: MantineTheme) => ({
        fontFamily: theme.other.fontFamily.primaryBold[0],
        input: {
          height: '48px',
          border: `1px solid ${theme.other.colors.borderDuodenary[0]}`,
          color: theme.other.colors.textUndenary[5],
          fontFamily: theme.other.fontFamily.primaryRegular[0],
          '&:focus': {
            border: `2px solid ${theme.other.colors.hoverTredenary[0]}`,
          },
          '&:hover': {
            cursor: 'pointer',
          },
          borderRadius: '4px',
          '&[data-invalid]': {
            border: `1px solid ${theme.other.colors.borderDenary[0]}`,
          },
        },
        label: {
          color: 'red',
        },
      }),
    },
    TextInput: {
      styles: (theme: MantineTheme) => ({
        fontFamily: theme.other.fontFamily.primaryserratBold[0],
        required: {
          color: '#353535',
        },
        wrapper: { margin: '0' },
        error: {
          color: theme.other.colors.textDenary[0],
          // paddingLeft: '16px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          // backgroundColor: theme.other.colors.error[0],
          margin: '0px',
          borderRadius: '4px',
          height: 'auto',
          fontSize: '16px',
          paddingTop: '5px',
          paddingBottom: '5px',
          paddingRight: '5px',
          fontFamily: theme.other.fontFamily.primaryRegular[0],
        },
        label: {
          fontSize: '16px',
          color: theme.other.colors.textUndenary[0],
          fontFamily: theme.other.fontFamily.primaryBold[0],
        },
        input: {
          fontSize: '16px',
        },
      }),
    },
    PasswordInput: {
      styles: (theme: MantineTheme) => ({
        fontFamily: theme.other.fontFamily.primaryserratBold[0],
        required: {
          color: '#353535',
        },
        innerInput: { marginTop: '5px', fontFamily: theme.other.fontFamily.primaryRegular[0] },
        wrapper: { margin: '0' },
        error: {
          color: theme.other.colors.textDenary[0],
          // paddingLeft: '16px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          // backgroundColor: theme.other.colors.error[0],
          margin: '0px',
          borderRadius: '4px',
          height: 'auto',
          fontSize: '18px',
          paddingTop: '5px',
          paddingBottom: '5px',
          paddingRight: '5px',
          fontFamily: theme.other.fontFamily.primaryserratBold[0],
        },
        label: {
          fontSize: '16px',
          fontFamily: theme.other.fontFamily.primaryBold[0],
        },
        input: {
          fontSize: '16px',
        },
      }),
    },
    NumberInput: {
      styles: (theme: MantineTheme) => ({
        fontFamily: theme.other.fontFamily.primaryserratBold[0],
        required: {
          color: theme.other.colors.textPrimary[0],
        },
        wrapper: { margin: '0' },
        error: {
          color: theme.other.colors.textDenary[0],
          paddingLeft: '16px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',

          margin: '0px',
          borderRadius: '4px',
          height: 'auto',
          fontSize: '16px',
          paddingTop: '5px',
          paddingBottom: '5px',
          paddingRight: '5px',
          fontFamily: theme.other.fontFamily.primaryRegular[0],
        },
        label: {
          fontSize: '16px',
          fontWeight: 'bold',
          color: theme.other.colors.textUndenary[0],
        },
        input: {
          fontSize: '16px',
        },
      }),
    },
    Select: {
      styles: (theme: MantineTheme) => ({
        fontFamily: theme.other.fontFamily.primaryserratBold[0],
        required: {
          color: '#353535',
        },

        dropdown: {
          border: '1px solid #004861',
          padding: '20px',
        },
        item: {
          color: theme.other.colors.textUndenary[0],
          '&:hover': {
            color: theme.other.colors.textUndenary[0],
            backgroundColor: '#e3e8ee',
            borderRadius: '0',
          },
          '&[data-selected]': {
            color: theme.other.colors.textUndenary[0],
            backgroundColor: '#e3e8ee',
            borderRadius: '0',
            '&, &:hover': {
              color: theme.other.colors.textUndenary[0],
              backgroundColor: '#e3e8ee',
              borderRadius: '0',
            },
          },
          '&[data-hovered]': {},
        },
        rightSection: { pointerEvents: 'none' },
        wrapper: { margin: '0' },
        error: {
          color: theme.other.colors.textDenary[0],
          paddingLeft: '16px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',

          margin: '0px',
          borderRadius: '4px',
          height: 'auto',
          fontSize: '16px',
          paddingTop: '5px',
          paddingBottom: '5px',
          paddingRight: '5px',
          fontFamily: theme.other.fontFamily.primaryRegular[0],
        },
        input: {
          fontSize: '16px',
          color: theme.other.colors.textUndenary[0],
        },
        label: {
          fontSize: '16px',
          color: theme.other.colors.textPrimary[0],
          fontWeight: 800,
        },
      }),
    },
    Radio: {
      styles: (theme: MantineTheme) => ({
        fontFamily: theme.other.fontFamily.primaryRegular[0],
        radio: {
          border: `1.5px solid ${theme.other.colors.borderUndenary}`,
          '&:checked': {
            border: `1.5px solid ${theme.other.colors.borderUndenary}`,
            backgroundColor: 'white',
          },
        },
        label: {
          fontSize: '16px',
        },
        input: {
          fontSize: '16px',
        },
        icon: {
          color: theme.other.colors.textSecondary,
        },
        required: {
          color: theme.other.colors.textSecondary[0],
        },
      }),
    },
    RadioGroup: {
      styles: (theme: MantineTheme) => ({
        fontFamily: theme.other.fontFamily.primaryRegular[0],
        label: {
          fontSize: '16px',
          color: theme.other.colors.textSecondary[0],
          fontWeight: 800,
        },
        required: {
          color: theme.other.colors.textSecondary[0],
        },
        input: {
          fontSize: '16px',
        },
      }),
    },
    Modal: {
      styles: () => ({
        overlay: {
          backgroundColor: '#326295',
          opacity: '0.8 !important',
        },
        content: {
          borderRadius: '20px !important',
        },
      }),
    },
    DatePicker: {
      styles: (theme: MantineTheme) => ({
        required: {
          color: theme.other.colors.textPrimary[0],
        },
        input: {
          fontSize: '16px',
          fontWeight: 'bold',
          color: theme.other.colors.textUndenary[0],
        },
        levelsGroup: {
          border: '1px solid #004861',
        },
        calendarHeaderLevel: {
          fontFamily: theme.other.fontFamily.primaryBold[0],
          color: theme.other.colors.textPrimary[0],
          fontSize: '18px',
          lineHeight: '24px',
          letterSpacing: '-0.25px',
        },
        weekday: {
          fontFamily: theme.other.fontFamily.primaryBold[0],
          color: theme.other.colors.textPrimary[0],
        },
        calender: {
          border: '1px solid #004861',
        },
        day: {
          color: theme.other.colors.textSecondary[0],
          fontFamily: theme.other.fontFamily.primaryBold[0],
          '&[data-selected]': {
            backgroundColor: theme.other.colors.bgPrimary[0],
            color: 'white',
            borderRadius: '0',
            '&:hover': {
              backgroundColor: '#D4F0FA',
              color: theme.other.colors.hoverSecondary[0],
            },
          },
          '&:disabled': {
            color: '#8096A2',
            fontFamily: theme.other.fontFamily.primaryRegular[0],
            border: 'none',
          },
          '&:hover': {
            backgroundColor: '#D4F0FA',
            color: theme.other.colors.hoverSecondary[0],
            borderRadius: '0',
            '&:disabled': {
              color: theme.other.colors.textQuattuordenary[0],
              fontFamily: theme.other.fontFamily.primaryRegular[0],
            },
          },
        },
      }),
    },
    DatePickerInput: {
      styles: (theme: MantineTheme) => ({
        levelsGroup: {
          border: '1px solid #004861',
        },
        required: {
          color: theme.other.colors.textPrimary[0],
        },
        calendarHeaderLevel: {
          fontFamily: theme.other.fontFamily.primaryBold[0],
          color: theme.other.colors.textSecondary[0],
          fontSize: '18px',
          lineHeight: '24px',
          letterSpacing: '-0.25px',
        },
        weekday: {
          fontFamily: theme.other.fontFamily.primaryBold[0],
          color: theme.other.colors.textSecondary[0],
        },
        calender: {
          border: '1px solid #004861',
        },
        day: {
          color: theme.other.colors.textSecondary[0],
          fontFamily: theme.other.fontFamily.primaryBold[0],
          '&[data-selected]': {
            backgroundColor: theme.other.colors.bgPrimary[0],
            color: 'white',
            borderRadius: '0',
            '&:hover': {
              backgroundColor: '#D4F0FA',
              color: theme.other.colors.hoverSecondary[0],
            },
          },
          '&:disabled': {
            color: '#8096A2',
            fontFamily: theme.other.fontFamily.primaryRegular[0],
            border: 'none',
          },
          '&:hover': {
            backgroundColor: '#D4F0FA',
            color: theme.other.colors.textSecondary[0],
            borderRadius: '0',
            '&:disabled': {
              color: theme.other.colors.textQuattuordenary[0],
              fontFamily: theme.other.fontFamily.primaryRegular[0],
            },
          },
        },
      }),
    },
    Checkbox: {
      styles: (theme: MantineTheme) => ({
        fontFamily: theme.other.fontFamily.primaryserratBold[0],
        label: {
          fontSize: '18px',
        },
        input: {
          '&:checked': {
            backgroundColor: theme.other.colors.bgPrimary[0],
            borderColor: theme.other.colors.borderPrimary[0],
          },
        },
      }),
    },
    SegmentedControl: {
      styles: (theme: MantineTheme) => ({
        root: {
          width: '200px',
          backgroundColor: 'transparent',
        },
        controlActive: {
          backgroundColor: 'transparent',
          borderRadius: '17px',
        },
        indicator: {
          backgroundColor: 'transparent',
          boxShadow: 'none',
        },
        label: {
          color: theme.other.colors.textPrimary[0],
          '&[data-active]': {
            backgroundColor: 'transparent',
            fontFamily: theme.other.fontFamily.primaryBold[0],
            textDecoration: 'underline',
            textDecorationThickness: '2px',
            textUnderlineOffset: '4px',
            color: theme.other.colors.textPrimary[0],
          },
        },
      }),
    },
    Accordion: {
      styles: (theme: MantineTheme) => ({
        label: {
          fontFamily: theme.other.fontFamily.primaryBold[0],
          color: theme.other.colors.textSecondary[0],
        },
      }),
    },
  },
};
