import {
  Field,
  withDatasour<PERSON><PERSON><PERSON><PERSON>,
  Text,
  Placeholder,
  RichText,
  LinkField,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { faCheck } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useRef } from 'react';
import PrintPageButton from 'components/Elements/PrintPageButton/PrintPageButton';
import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import { useRouter } from 'next/router';
import { useNavigationDirection } from 'src/hooks/useNavigationDirection';
import { TokenData } from 'components/MyAccount/Login/Login';
import jwt from 'jsonwebtoken';
import { setCookie } from 'cookies-next';
import * as CONSTANTS from 'src/utils/constants';
import PrintSelectedPlanCard from 'components/common/PrintSelectedPlanCard/PrintSelectedPlanCard';
import { getRedirectionUrl } from 'src/utils/getRedirectionUrl';
import { setOopsRedirect } from 'src/stores/enrollmentSlice';
import axios from 'axios-1.4';
import { fromAddress, setKey, setLanguage } from 'react-geocode';
import { PaymentLocation } from 'src/services/PaymentLocationsAPI/types';
import InfoText from 'components/common/InfoText/InfoText';
import { isTxu } from 'src/utils/util';
import dayjs from 'dayjs';
import Button from 'components/Elements/Button/Button';
import { formatPhoneNumber } from 'src/utils/fuseManager';
import { useI18n } from 'next-localization';
import { QueryParamsMapType } from 'src/utils/query-params-mapping';

type MaxiWelcomeBlockWithPlanDetailsProps = ComponentProps & {
  fields: {
    ConfirmationNumberLabel: Field<string>;
    StartDateLabel: Field<string>;
    PrintText: Field<string>;
    PlanSummaryLabel: Field<string>;
    NextStepLabel: Field<string>;
    PersonalInfoLabel: Field<string>;
    AccountNumberLabel: Field<string>;
    ServiceInfoLabel: Field<string>;
    BillingAddressLabel: Field<string>;
    SecondaryContactLabel: Field<string>;
    PreferencesLabel: Field<string>;
    PaymentInfoTitle: Field<string>;
    TotalDepositAmountLabel: Field<string>;
    TotalDepositAmountDescription50: Field<string>;
    TotalDepositAmountDescription100: Field<string>;
    PaymentMethodLabel: Field<string>;
    DepositPaidLabel50: Field<string>;
    DepositPaidLabel100: Field<string>;
    CardHolderName: Field<string>;
    CardNumberLabel: Field<string>;
    PriorBalanceLabel: Field<string>;
    PriorBalancePaidLabel: Field<string>;
    ExpirationLabel: Field<string>;
    TotalDueAmountLabel: Field<string>;
    BillingZipcodeLabel: Field<string>;
    NoteLabel: Field<string>;
    NoteDescription: Field<string>;
    PayByPhoneDescrption: Field<string>;
    PayByLocationDescription: Field<string>;
    DepositPaymentByLocation: Field<string>;
    NearestLocation: Field<string>;
    LocationSearchDistance: Field<string>;
    ContinueToMyAccountButton: Field<string>;
    ResidentialMyAccountPageUrl: LinkField;
    BussinessMyAccountPageUrl: LinkField;
    ClosedAccountUrl: LinkField;
    AwaitingSDAccountUrl: LinkField;
    POBoxLabel: Field<string>;
    SolarDisclaimerText: Field<string>;
    PlanSummary: Field<string>;
    WhatsNextLabel: Field<string>;
    // WhatsNextLabel: Field<string>; Commented as per the Business requirement
    // WhatsNextDescription: Field<string>; Commented as per the Business requirement
    EnrolledCommunicationMessage: Field<string>;
    NotEnrolledCommunicationMessage: Field<string>;
    AutoPayEnrolledMessage: Field<string>;
    NotAutoPayEnrolledMessage: Field<string>;
    MobilePhoneLabel: Field<string>;
    ESIIDLabel: Field<string>;
    ServiceType: Field<string>;
    YesLabel: Field<string>;
    NoLabel: Field<string>;
    SwitchLabel: Field<string>;
    MoveinLabel: Field<string>;
    UsernameLabel: Field<string>;
  };
};

const MaxiWelcomeBlockWithPlanDetailsProps = (
  props: MaxiWelcomeBlockWithPlanDetailsProps
): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  // if (isPageEditing) return <PageBuilder componentName="MaxiWelcomeBlockWithPlanDetails" />;
  let dispatch: ReturnType<typeof useAppDispatch>;
  let enrollmentInfo = undefined;
  let serviceInfo = undefined;
  let enrollment = undefined;
  let isPaperlessBilling = undefined;
  let isAutoPay = undefined;
  let isSameAddress = undefined;
  let sunRunOrderInfo = undefined;
  if (!isPageEditing) {
    dispatch = useAppDispatch();
    enrollmentInfo = useAppSelector((state) => state.enrollment.enrollmentInfo);
    serviceInfo = useAppSelector((state) => state.enrollment.serviceInfo);
    enrollment = useAppSelector((state) => state.enrollment);
    isPaperlessBilling = useAppSelector((state) => state.enrollment.isPaperlessBilling);
    isAutoPay = useAppSelector((state) => state.enrollment.isAutoPay);
    isSameAddress = useAppSelector((state) => state.enrollment.billingInfo.isSameAddress);
    sunRunOrderInfo = useAppSelector((state) => state.enrollment.SunRunOrderInfo);
  }
  const printRef = useRef<HTMLDivElement>(null);
  const currentDate = new Date();
  const router = useRouter();
  useNavigationDirection();
  const { locale } = useI18n();
  const [paymentLocation, setPaymentLocation] = useState('');

  const enableMyAccountBtn = enrollment?.isCoaSuccess;
  const showMyAccountBtn = enrollment?.showMyAccountBtn;
  const { cint } = router.query as QueryParamsMapType;
  //const [isPayByPhone] = useState(false);
  //const [isPayAtLocation] = useState(false);
  //const [isDepositRequired] = useState(true);
  //const [isHalfScheduled] = useState(false);
  const userName = enrollment?.userName;
  const [isPayByPhone] = useState(enrollment?.paymentInfo?.paymentType === '2');
  const [isPayAtLocation] = useState(enrollment?.paymentInfo?.paymentType === '3');
  const [isHalfScheduled] = useState(enrollment?.paymentInfo?.paymentType === '1');
  const [isDepositRequired] = useState(
    enrollment?.paymentInfo?.paymentType === '0' || enrollment?.paymentInfo?.paymentType === '1'
      ? true
      : false
  );
  const priorDebt = enrollment?.priorDebtInfo?.PriorDebt;
  const thresholdPassed = enrollment?.priorDebtInfo?.ThresholdPassed;
  const priorDebtPaid = thresholdPassed ? true : enrollment?.paymentInfo?.priorDebtPaid;
  let totalPriorBalance = 0;
  if (priorDebt?.length > 0) {
    totalPriorBalance = priorDebt?.reduce((a, v) => (a = a + v.TotalDebt), 0);
  }
  const depositAmount = enrollment?.depositAmount;
  const depositAmountHalf = depositAmount / 2;

  useEffect(() => {
    const GetPaymentLocation = async () => {
      const address =
        enrollment?.serviceInfo.houseNbr +
        ' ' +
        enrollment?.serviceInfo.street +
        ' ' +
        enrollment?.serviceInfo.unit +
        ' ' +
        enrollment?.serviceInfo.city +
        ',' +
        enrollment?.serviceInfo.state +
        ' ' +
        enrollment?.serviceInfo.postalCode;
      setKey(process.env.NEXT_PUBLIC_GOOGLE_APIKEY as string);
      setLanguage('en');

      fromAddress(address).then(async ({ results }) => {
        let payatlocation = '';
        const { lat, lng } = results[0].geometry.location;
        const loc = await axios.get('/api/paymentlocation', {
          params: {
            lat: lat,
            long: lng,
            distance: props.fields.LocationSearchDistance.value,
          },
        });

        if (loc.data.result && loc.data.result[0]) {
          const locObj: PaymentLocation = loc.data.result[0];

          const nearestLocation =
            '<p>' +
            locObj.name +
            '</p><p>' +
            locObj.street +
            '</p><p>' +
            locObj.city +
            ', ' +
            locObj.state +
            ' ' +
            locObj.zipCode +
            '</p><p>' +
            locObj.phoneNumber +
            '</p>';

          payatlocation = props.fields.DepositPaymentByLocation.value.replace(
            '{location}',
            nearestLocation
          );
        } else {
          payatlocation = props.fields.DepositPaymentByLocation.value.replace(
            '{location}',
            props.fields.NearestLocation.value
          );
        }

        setPaymentLocation(payatlocation);
      });
    };

    if (isPayAtLocation) GetPaymentLocation();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [locale()]);

  const billingAddress = isSameAddress
    ? enrollment?.serviceInfo.houseNbr +
      ' ' +
      enrollment?.serviceInfo.street +
      ' ' +
      enrollment?.serviceInfo.unit +
      ' ' +
      enrollment?.serviceInfo.city +
      ', ' +
      enrollment?.serviceInfo.state +
      ' ' +
      enrollment?.serviceInfo.postalCode
    : enrollment?.serviceInfo.poBox === ''
    ? enrollment?.billingInfo.billingStreetNumber +
      ' ' +
      enrollment?.billingInfo.billingStreetAddress +
      ' ' +
      enrollment?.billingInfo.billingAptOrUnit +
      ' ' +
      enrollment?.billingInfo.billingCity +
      ', ' +
      enrollment?.billingInfo.billingState +
      ' ' +
      enrollment?.billingInfo.billingZipCode
    : `${props.fields.POBoxLabel.value}` +
      ' ' +
      enrollment?.serviceInfo.poBox +
      ' ' +
      enrollment?.serviceInfo.poBoxCity +
      ' ' +
      enrollment?.serviceInfo.poBoxState +
      ' ' +
      enrollment?.serviceInfo.poBoxZipCode;

  const totalDepositDueFull = isHalfScheduled
    ? priorDebtPaid
      ? 0 + depositAmountHalf
      : depositAmountHalf + totalPriorBalance
    : priorDebtPaid
    ? 0
    : thresholdPassed
    ? 0
    : totalPriorBalance;

  const myAccountAccessToken = enrollment?.myAccountInfo;

  async function redirectToNextPage() {
    //generateAccessToken();
    const decodedToken = jwt.decode(myAccountAccessToken.access_token) as TokenData;
    const customerType = decodedToken?.customer_classification?.toString().toLowerCase();
    const isBusinessUser = customerType !== 'unknown' && customerType !== CONSTANTS.CUSTOMER_TYPE;
    const responseData = await axios.get(
      `/api/usercheck/validateusername?username=${userName}&bp_number=${
        isBusinessUser ? decodedToken.org_bp_number : decodedToken.bp_number
      }`
    );
    const nextStep = responseData.data.result?.hasInActiveContracts;
    if (!nextStep) {
      generateAccessToken();
      const myAccountPageUrl = getRedirectionUrl(
        customerType,
        CONSTANTS.CUSTOMER_TYPE,
        props.fields.ResidentialMyAccountPageUrl.value.href,
        props.fields.BussinessMyAccountPageUrl.value.href
      );
      if (myAccountPageUrl !== '') {
        router.push({
          pathname: myAccountPageUrl,
        });
      }
    } else {
      console.log(props.fields);
      if (responseData.data.result?.message === 'AwaitingSDErrorMessage') {
        onErrorRedirect(userName, 'awaitingsd', props.fields.AwaitingSDAccountUrl.value.href || '');
      } else if (responseData.data.result?.message === 'ClosedAccountErrorMessage') {
        onErrorRedirect(userName, 'closed', props.fields.ClosedAccountUrl.value.href || '');
      }
    }
  }

  const onErrorRedirect = (username: string, type: string, path: string) => {
    //const encodedUsername = encodeURIComponent(username);
    const queryParams = {
      action: type,
      username: username,
    };
    console.log(queryParams);
    router.push({
      pathname: path,
      query: { ...queryParams },
    });
  };

  const cookieDomain = process.env.NEXT_PUBLIC_COOKIES_OPTIONS_DOMAIN;

  const generateAccessToken = () => {
    if (myAccountAccessToken.access_token) {
      if (!isPageEditing) {
        dispatch(setOopsRedirect(false));
      }
      setCookie('AuthToken', myAccountAccessToken.access_token, {
        path: '/',
        sameSite: 'none',
        secure: true,
        domain: cookieDomain,
        maxAge: myAccountAccessToken.expires_in,
      });
    }
  };

  console.log('Payment Location:', paymentLocation);

  return (
    <div>
      <div className="bg-bgQuaternary w-[100px] h-[100px] rounded-full flex items-center justify-center m-auto mt-8">
        <FontAwesomeIcon className="text-textQuinary text-plus5" icon={faCheck} />
      </div>
      <div
        className="confirmation w-full max-w-[924px] px-0 sm:px-[15px] flex flex-col gap-5 print:w-11/12 print:m-5 mt-[40px] mb-0 sm:mb-[20px] sm:my-[40px]"
        ref={printRef}
      >
        <div className="flex flex-col mt-[4px] justify-center px-[15px] sm:px-0">
          <Text
            tag="p"
            className="flex justify-center leading-[30px] text-plus2 font-primaryBold md:text-lg mt-[20px] text-textQuattuordenary text-center"
            field={{
              value: props.fields?.ConfirmationNumberLabel?.value.replace(
                '{ConfirmationId}',
                enrollmentInfo?.serviceAccountNumber.trim()
              ),
            }}
          />
          <p className="flex justify-center leading-[24px] text-minus1 font-primaryRegular md:text-lg mt-[12px]">
            {currentDate?.toLocaleString()}
          </p>
          <div className="text-center mt-[28px] my-2">
            <PrintPageButton
              label={props.fields.PrintText.value}
              printRef={printRef}
              pageName="mini-confirmation"
            />
          </div>
          {(isPayByPhone || isPayAtLocation) && (
            <div className="flex flex-row md:items-center sm:text-center">
              <Text
                tag="p"
                className="font-primaryRegular text-textQuattuordenary text-plus2 md:flex-grow md:text-plus3 pb-4"
                field={props.fields.NextStepLabel}
              />
            </div>
          )}
          {isPayByPhone && (
            <div className="flex flex-row md:items-center justify-center sm:text-center mb-4">
              <RichText
                className={`font-primaryRegular text-textQuattuordenary text-minus1 sm:text-base leading-[30px] max-w-[635px]`}
                field={{ value: props.fields.PayByPhoneDescrption.value }}
              />
            </div>
          )}
          {isPayAtLocation && (
            <div className="flex flex-row md:items-center">
              <RichText
                className="font-primaryRegular text-textQuattuordenary text-minus1 md:text-base"
                field={{ value: paymentLocation }}
              />
            </div>
          )}
          <hr className="border-solid border-borderOctodenary border-1 my-6 sm:hidden" />
        </div>
        <div className="sm:border bg-bgQuinary border-borderQuaternary px-[20px] sm:p-[30px] rounded-[20px]">
          <div>
            <div className="flex flex-col gap-3 sm:gap-0 sm:flex-row">
              <div className="flex flex-col sm:w-1/3 w-full sm:pr-3">
                <Text
                  tag="p"
                  className="font-primaryBold text-textQuattuordenary text-base sm:text-plus2 leading-[30px]"
                  field={props.fields?.AccountNumberLabel}
                />

                <Text
                  tag="p"
                  className="font-primaryRegular text-textQuattuordenary text-[16px] leading-[24px] py-2 break-word"
                  field={{ value: enrollmentInfo?.contractAccountNumber }}
                />
              </div>

              <div className="flex flex-col sm:w-1/3 w-full sm:pr-3">
                <Text
                  tag="p"
                  className="font-primaryBold text-textQuattuordenary text-base sm:text-plus2 "
                  field={props.fields.PersonalInfoLabel}
                />

                <Text
                  tag="p"
                  className="font-primaryRegular text-textQuattuordenary text-[16px] leading-[24px] py-1 break-word"
                  field={{
                    value: `${enrollment?.customerInfo.firstName} ${enrollment?.customerInfo.lastName}`,
                  }}
                />

                <Text
                  tag="p"
                  className="font-primaryRegular text-textQuattuordenary text-[14px] leading-[24px] py-1 break-word"
                  field={{
                    value: `${enrollment?.customerInfo.email}`,
                  }}
                />
                <Text
                  tag="p"
                  className="font-primaryRegular text-textQuattuordenary text-[16px] leading-[24px] py-1 break-word"
                  field={{
                    value: enrollment?.customerInfo.phoneNumber
                      ? formatPhoneNumber(enrollment?.customerInfo.phoneNumber.toString())
                      : '',
                  }}
                />
                <div className="flex flex-row items-center gap-2 py-1">
                  <Text
                    tag="p"
                    className="font-primaryRegular text-textQuattuordenary text-[16px] leading-[24px] break-word"
                    field={{ value: props.fields.MobilePhoneLabel.value + ':' }}
                  />
                  <Text
                    tag="p"
                    className="font-primaryRegular text-textQuattuordenary text-[16px] leading-[24px] break-word"
                    field={{
                      value: enrollment?.customerInfo?.isMobile
                        ? props.fields.YesLabel.value
                        : props.fields.NoLabel.value,
                    }}
                  />
                </div>
              </div>

              <div className="flex flex-col sm:w-1/3 w-full">
                <Text
                  tag="p"
                  className="font-primaryBold text-textQuattuordenary text-base sm:text-plus2"
                  field={props.fields.ServiceInfoLabel}
                />

                <Text
                  tag="p"
                  className="font-primaryRegular text-textQuattuordenary text-[16px] leading-[24px] py-1 break-word"
                  field={{
                    value: `${serviceInfo?.houseNbr} ${serviceInfo?.street} ${serviceInfo?.unit} ${serviceInfo?.city}, ${serviceInfo?.state} ${serviceInfo?.postalCode}`,
                  }}
                />
                <div className="flex flex-row items-center gap-2 py-1">
                  <Text
                    tag="p"
                    className="font-primaryRegular text-textQuattuordenary text-[16px] leading-[24px] break-word"
                    field={{ value: props.fields.ESIIDLabel.value + ':' }}
                  />
                  <Text
                    tag="p"
                    className="font-primaryRegular text-textQuattuordenary text-[16px] leading-[24px] break-word"
                    field={{ value: serviceInfo?.esiid }}
                  />
                </div>
                <div className="flex flex-wrap items-center gap-2 py-1">
                  <Text
                    tag="p"
                    className="font-primaryRegular text-textQuattuordenary text-[16px] leading-[24px] break-word"
                    field={{ value: props.fields.StartDateLabel.value + ':' }}
                  />
                  <Text
                    tag="p"
                    className="font-primaryRegular text-textQuattuordenary text-[16px] leading-[24px] break-word"
                    field={{ value: serviceInfo?.startDate }}
                  />
                </div>
                <div className="flex flex-row items-center gap-2 py-2">
                  <Text
                    tag="p"
                    className="font-primaryRegular text-textQuattuordenary text-[16px] leading-[24px] break-word"
                    field={{ value: props.fields.ServiceType.value + ':' }}
                  />
                  <Text
                    tag="p"
                    className="font-primaryRegular text-textQuattuordenary text-[16px] leading-[24px] break-word"
                    field={{
                      value:
                        cint === '4'
                          ? props.fields.MoveinLabel.value
                          : props.fields.SwitchLabel.value,
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-3 sm:gap-0 sm:flex-row my-10 sm:my-20">
            <div className="flex flex-col sm:w-1/3 w-full sm:pr-3">
              <Text
                tag="p"
                className="font-primaryBold text-textQuattuordenary text-base sm:text-plus2"
                field={props.fields.BillingAddressLabel}
              />

              <Text
                tag="p"
                className="font-primaryRegular text-textQuattuordenary text-[16px] leading-[24px] pt-3 break-word"
                field={{ value: billingAddress != null ? billingAddress : '' }}
              />
            </div>
            {/* <div className="flex flex-col sm:w-1/3 w-full">
              <Text
                tag="p"
                className="font-primaryBold text-textQuattuordenary sm:text-plus2 leading-[24px] "
                field={props.fields.StartDateLabel}
              />

              <Text
                tag="p"
                className="font-primaryRegular text-textQuattuordenary text-[13px] leading-[24px] pt-3 pl-2  break-word"
                field={{ value: enrollment.startDate }}
              />
            </div> */}

            <div className="flex flex-col sm:w-1/3 w-full sm:pr-3">
              <Text
                tag="p"
                className="font-primaryBold text-textQuattuordenary text-base sm:text-plus2"
                field={props.fields.SecondaryContactLabel}
              />

              <Text
                tag="p"
                className="font-primaryRegular text-textQuattuordenary text-[16px] leading-[24px] pt-3 break-word"
                field={{
                  value: `${enrollment?.billingInfo.secondaryAccountFirstName} ${enrollment?.billingInfo.secondaryAccountLastName}`,
                }}
              />
            </div>

            <div className="flex flex-col sm:w-1/3 w-full">
              <Text
                tag="p"
                className="font-primaryBold text-textQuattuordenary text-base sm:text-plus2"
                field={props.fields.PreferencesLabel}
              />

              <div className="max-w-[200px]">
                <Text
                  tag="p"
                  className="font-primaryRegular text-textQuattuordenary text-[16px] leading-[24px] py-1 break-word"
                  field={{
                    value: enrollment?.languageOptionalValue,
                  }}
                />
                <Text
                  tag="p"
                  className="font-primaryRegular text-textQuattuordenary text-[16px] leading-[24px] py-1 break-word"
                  field={{
                    value: isPaperlessBilling
                      ? props.fields?.EnrolledCommunicationMessage?.value
                      : props.fields?.NotEnrolledCommunicationMessage?.value,
                  }}
                />
                {isAutoPay && (
                  <InfoText
                    isRow={isTxu}
                    showColon={false}
                    label=""
                    value={
                      isAutoPay
                        ? props.fields?.AutoPayEnrolledMessage?.value
                        : props.fields?.NotAutoPayEnrolledMessage?.value
                    }
                  />
                )}
                {userName !== '' && (
                  <>
                    <Text
                      tag="p"
                      className="text-textQuattuordenary font-primaryBold text-base sm:text-plus2 pb-4 mt-5"
                      field={props.fields.UsernameLabel}
                    />
                    <InfoText isRow={isTxu} label="" value={userName} />
                  </>
                )}
              </div>
            </div>
          </div>

          {/* <hr className="border-solid border-borderOctodenary border-1" /> */}
          {isDepositRequired && (
            <div>
              <Text
                tag="p"
                field={props.fields.PaymentInfoTitle}
                className="text-base sm:text-plus2 font-primaryBold text-textQuattuordenary my-4"
              />
              <div className="flex flex-col sm:flex-row gap-8">
                <div className="flex flex-col gap-4 sm:basis-[50%]">
                  <div>
                    <Text
                      tag="p"
                      className="font-primaryBold text-textQuattuordenary text-minus1 sm:text-base"
                      field={props.fields.TotalDepositAmountLabel}
                    />
                    <InfoText isRow={isTxu} label="" value={'$' + depositAmount.toFixed(2)} />
                    <InfoText
                      isRow={isTxu}
                      label=""
                      value={
                        isHalfScheduled
                          ? props.fields?.TotalDepositAmountDescription50?.value
                          : props.fields?.TotalDepositAmountDescription100?.value
                      }
                    />
                  </div>
                  <div>
                    <Text
                      tag="p"
                      className="font-primaryBold text-textQuattuordenary text-minus1 sm:text-base"
                      field={{
                        value: `${
                          isHalfScheduled
                            ? props.fields?.DepositPaidLabel50?.value
                            : props.fields?.DepositPaidLabel100?.value
                        }`,
                      }}
                    />
                    <InfoText
                      isRow={isTxu}
                      label=""
                      value={
                        isHalfScheduled
                          ? '$' + depositAmountHalf.toFixed(2)
                          : '$' + depositAmount.toFixed(2)
                      }
                    />
                  </div>
                  <div>
                    {priorDebtPaid ? (
                      <Text
                        tag="p"
                        className="font-primaryBold text-textQuattuordenary text-minus1 sm:text-base"
                        field={props.fields.PriorBalancePaidLabel}
                      />
                    ) : (
                      <Text
                        tag="p"
                        className="font-primaryBold text-textQuattuordenary text-minus1 sm:text-base"
                        field={props.fields.PriorBalanceLabel}
                      />
                    )}
                    {thresholdPassed ? (
                      <InfoText isRow={isTxu} label="" value={'$' + totalPriorBalance.toFixed(2)} />
                    ) : priorDebtPaid ? (
                      <InfoText isRow={isTxu} label="" value={'$' + totalPriorBalance.toFixed(2)} />
                    ) : (
                      <InfoText isRow={isTxu} label="" value={'$' + totalPriorBalance.toFixed(2)} />
                    )}
                  </div>

                  <div>
                    <Text
                      tag="p"
                      className="font-primaryBold text-textQuattuordenary text-minus1 sm:text-base"
                      field={props.fields.TotalDueAmountLabel}
                    />
                    <InfoText isRow={isTxu} label="" value={'$' + totalDepositDueFull.toFixed(2)} />
                  </div>
                  {isHalfScheduled && (
                    <div>
                      <Text
                        tag="p"
                        className="font-primaryBold text-textQuattuordenary text-minus1 sm:text-base"
                        field={props.fields.NoteLabel}
                      />
                      <Text
                        tag="p"
                        className="font-primaryRegular text-textQuattuordenary text-minus1 sm:text-base w-full"
                        field={{
                          value: props.fields.NoteDescription?.value.replace(
                            '{pendingDeposit}',
                            depositAmountHalf.toFixed(2)
                          ),
                        }}
                      />
                    </div>
                  )}
                </div>
                <div className="flex flex-col gap-4 sm:basis-[50%]">
                  <div>
                    <Text
                      tag="p"
                      className="font-primaryBold text-textQuattuordenary text-minus1 sm:text-base"
                      field={props.fields.PaymentMethodLabel}
                    />
                    <InfoText
                      isRow={isTxu}
                      label=""
                      value={enrollment?.paymentInfo?.paymentMethod}
                    />
                  </div>
                  <div>
                    <Text
                      tag="p"
                      className="font-primaryBold text-textQuattuordenary text-minus1 sm:text-base"
                      field={props.fields.CardHolderName}
                    />
                    <InfoText
                      isRow={isTxu}
                      label=""
                      value={enrollment?.paymentInfo?.cardHolderName}
                    />
                  </div>
                  <div>
                    <Text
                      tag="p"
                      className="font-primaryBold text-textQuattuordenary text-minus1 sm:text-base"
                      field={props.fields.CardNumberLabel}
                    />
                    <InfoText
                      isRow={isTxu}
                      label=""
                      value={'XXXX-XXXX-XXXX-' + enrollment?.paymentInfo?.cardNumber}
                    />
                  </div>
                  <div>
                    <Text
                      tag="p"
                      className="font-primaryBold text-textQuattuordenary text-minus1 sm:text-base"
                      field={props.fields.ExpirationLabel}
                    />
                    <InfoText
                      isRow={isTxu}
                      label=""
                      value={dayjs(enrollment?.paymentInfo?.expirationDate)
                        .format('MM/DD/YYYY')
                        .toString()}
                    />
                  </div>
                  <div>
                    <Text
                      tag="p"
                      className="font-primaryBold text-textQuattuordenary text-minus1 sm:text-base"
                      field={props.fields.BillingZipcodeLabel}
                    />
                    <InfoText isRow={isTxu} label="" value={enrollment?.paymentInfo?.zipCode} />
                  </div>
                </div>
              </div>
            </div>
          )}
          <div className="">
            <Placeholder
              name="jss-welcomeblock-details"
              rendering={props.rendering}
              render={(components) => <div id="welcomeblock-container">{components}</div>}
            />
            {sunRunOrderInfo?.confirmation !== '' && (
              <RichText
                tag="p"
                className="font-primaryRegular text-textQuattuordenary text-minus1 text-[18px] leading-[28px] mt-[14px] inline-link"
                field={props.fields.SolarDisclaimerText}
              />
            )}
          </div>
        </div>
        <div className="hidden print:block">
          <PrintSelectedPlanCard
            fields={{
              PlanSummary: props.fields.PlanSummary,
              WhatsNextLabel: props.fields.WhatsNextLabel,
            }}
            rendering={props.rendering}
            params={props.params}
          />
        </div>
      </div>
      <div className="px-[15px] mt-6">
        {showMyAccountBtn && (
          <Button className="" onClick={redirectToNextPage} disabled={!enableMyAccountBtn}>
            {props.fields?.ContinueToMyAccountButton?.value}
          </Button>
        )}
      </div>
    </div>
  );
};

export { MaxiWelcomeBlockWithPlanDetailsProps };
const Component = withDatasourceCheck()<MaxiWelcomeBlockWithPlanDetailsProps>(
  MaxiWelcomeBlockWithPlanDetailsProps
);
export default aiLogger(Component, Component.name);
