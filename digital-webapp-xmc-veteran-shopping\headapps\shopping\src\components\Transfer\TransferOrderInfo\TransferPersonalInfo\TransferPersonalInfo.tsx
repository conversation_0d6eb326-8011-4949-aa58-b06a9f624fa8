import { UseFormReturnType } from '@mantine/form';
import {
  Text,
  Field,
  withDatasourceCheck,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useAppSelector } from 'src/stores/store';
import { QueryParamsMapType } from 'src/utils/query-params-mapping';
import { TransferOrderInfoFormType } from '../TransferOrderInfoContainer/TransferOrderInfoContainer';

type TransferPersonalInfoProps = ComponentProps & {
  fields: {
    Header: Field<string>;
    Name: Field<string>;
    NewServiceAddress: Field<string>;
    AccountNumber: Field<string>;
  };
  form: UseFormReturnType<TransferOrderInfoFormType>;
};

const TransferPersonalInfo = (props: TransferPersonalInfoProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let personalInfo = undefined;
  let authUser = undefined;
  if (!isPageEditing) {
    personalInfo = useAppSelector((state) => state.transfer?.personalInfo);
    authUser = useAppSelector((state) => state.authuser);
  }
  const router = useRouter();
  const { contractaccount } = router.query as QueryParamsMapType;
  return (
    <div className="flex flex-col w-full max-w-[832px] mt-8">
      <div className="text-plus2 sm:text-plus2 text-textQuattuordenary font-primaryBold  mb-0 sm:mb-[35px] sm:px-0 px-6 mt-0  sm:my-2 ipad:pl-[20px] wide:pl-[20px]">
        {props.fields.Header.value}
      </div>
      <div className=" px-4 sm:px-0 ipad:pl-0 ">
        <div className=" w-full   mt-6 relative sm:mt-0 gap-4">
          <div>
            <div className="flex flex-col pb-4">
              <span className="font-primaryBold  text-textQuattuordenary text-minus1">
                <Text className="" field={props.fields.Name} />
              </span>
              <span className="text-textQuattuordenary text-minus1">
                {authUser?.userFirstName} {authUser?.userLastName}
              </span>
            </div>
            <div className="flex flex-col pb-4">
              <span className="font-primaryBold   text-textQuattuordenary text-minus1">
                <Text field={props.fields.AccountNumber} />
              </span>
              <span className="text-textQuattuordenary text-minus1"> {contractaccount}</span>
            </div>
            <div className="flex flex-col pb-4">
              <span className="font-primaryBold  text-textQuattuordenary text-minus1">
                <Text field={props.fields.NewServiceAddress} />
              </span>
              <span className="text-textQuattuordenary text-minus1">
                {personalInfo?.newServiceAddress.display_text}
              </span>
            </div>
          </div>
        </div>
      </div>
      <hr className='h-px my-8 border-borderVigintiternary border w-full px-4 max-w-[830px] m-auto wide:max-w-full ipad:max-w-full' />
    </div>
  );
};

export { TransferPersonalInfo };
const Component = withDatasourceCheck()<TransferPersonalInfoProps>(TransferPersonalInfo);
export default aiLogger(Component, Component.name);
