/* eslint-disable */
// Do not edit this file, it is auto-generated at build time!
// See scripts/generate-component-builder/index.ts to modify the generation of this file.

import dynamic from 'next/dynamic';
import { ComponentBuilder } from '@sitecore-jss/sitecore-jss-nextjs';

import { <PERSON><PERSON><PERSON><PERSON>rapper, FEaaSWrapper } from '@sitecore-jss/sitecore-jss-nextjs';

import * as CdpPageView from 'src/components/CdpPageView';
import * as ColumnSplitter from 'src/components/ColumnSplitter';
import * as Container from 'src/components/Container';
import * as FEAASScripts from 'src/components/FEAASScripts';
import * as Image from 'src/components/Image';
import * as LinkList from 'src/components/LinkList';
import * as Navigation from 'src/components/Navigation';
import * as PageContent from 'src/components/PageContent';
import * as PartialDesignDynamicPlaceholder from 'src/components/PartialDesignDynamicPlaceholder';
import * as Promo from 'src/components/Promo';
import * as RichText from 'src/components/RichText';
import * as RowSplitter from 'src/components/RowSplitter';
import * as Title from 'src/components/Title';
import * as AdditionalProducts from 'src/components/AdditionalProducts/AdditionalProducts';
import * as AddonPlanCard from 'src/components/AddonPlanCard/AddonPlanCard';
import * as ExtendedAddonPlanCard from 'src/components/AddonPlanCard/ExtendedAddonPlanCard';
import * as AddonPlanCardList from 'src/components/AddonPlanCardList/AddonPlanCardList';
import * as AddressTypeAhead from 'src/components/AddressTypeAhead/AddressTypeAhead';
import * as AMBPathToPlan from 'src/components/AMBPathToPlan/AMBPathToPlan';
import * as AcquisitionContent from 'src/components/common/AcquisitionContent/AcquisitionContent';
import * as AdobeDataLayer from 'src/components/common/AdobeDataLayer/AdobeDataLayer';
import * as AMBFooter from 'src/components/common/AMBFooter/AMBFooter';
import * as index from 'src/components/common/AMBFooter/index';
import * as AMBHeader from 'src/components/common/AMBHeader/AMBHeader';
import * as AuthenticationConfirmationContainer from 'src/components/common/AuthenticationConfirmationContainer/AuthenticationConfirmationContainer';
import * as BannerTitle from 'src/components/common/BannerTitle/BannerTitle';
import * as Calendar from 'src/components/common/Calendar/Calendar';
import * as Carousel from 'src/components/common/Carousel/Carousel';
import * as CharitySelection from 'src/components/common/CharitySelection/CharitySelection';
import * as CharitySelectiontypes from 'src/components/common/CharitySelection/componentprops/CharitySelection.types';
import * as CharityCard from 'src/components/common/CharitySelection/elements/CharityCard';
import * as GenesysChat from 'src/components/common/Chat/GenesysChat/GenesysChat';
import * as GenesysChatprops from 'src/components/common/Chat/GenesysChat/componentprops/GenesysChatprops';
import * as FloatingButton from 'src/components/common/Chat/GenesysChat/elements/FloatingButton';
import * as GenesysChatFloatingLink from 'src/components/common/Chat/GenesysChat/elements/GenesysChatFloatingLink';
import * as GenesysChatToggleLink from 'src/components/common/Chat/GenesysChat/elements/GenesysChatToggleLink';
import * as GenesysChatToggleLinkMobile from 'src/components/common/Chat/GenesysChat/elements/GenesysChatToggleLinkMobile';
import * as ChatFunctions from 'src/components/common/Chat/GenesysChat/functions/ChatFunctions';
import * as CommonContainerWithoutMenu from 'src/components/common/CommonContainerWithoutMenu/CommonContainerWithoutMenu';
import * as CookieConsentBanner from 'src/components/common/CookieConsentBanner/CookieConsentBanner';
import * as CustomerSupport from 'src/components/common/CustomerSupport/CustomerSupport';
import * as CustomProgressBar from 'src/components/common/CustomProgressBar/CustomProgressBar';
import * as Disclaimers from 'src/components/common/Disclaimers/Disclaimers';
import * as Divider from 'src/components/common/Divider/Divider';
import * as DynamicHTML from 'src/components/common/DynamicHTML/DynamicHTML';
import * as EditableTextBox from 'src/components/common/EditableTextBox/EditableTextBox';
import * as ExitPopup from 'src/components/common/ExitPopup/ExitPopup';
import * as FooterDisclaimer from 'src/components/common/FooterDisclaimer/FooterDisclaimer';
import * as GlobalLinkClickListener from 'src/components/common/GlobalLinkClickListener/GlobalLinkClickListener';
import * as InfoText from 'src/components/common/InfoText/InfoText';
import * as LanguageSelector from 'src/components/common/LanguageSelector/LanguageSelector';
import * as LanguageSelectorMobile from 'src/components/common/LanguageSelectorMobile/LanguageSelectorMobile';
import * as Avatar from 'src/components/common/LeftMenu/Avatar/Avatar';
import * as LeftMenuContainer from 'src/components/common/LeftMenu/LeftMenuContainer/LeftMenuContainer';
import * as MenuList from 'src/components/common/LeftMenu/MenuList/MenuList';
import * as MobileMenu from 'src/components/common/LeftMenu/MobileMenu/MobileMenu';
import * as SiteLogo from 'src/components/common/LeftMenu/SiteLogo/SiteLogo';
import * as LeftNavigation from 'src/components/common/LeftNavigation/LeftNavigation';
import * as LoaderModal from 'src/components/common/LoaderModal/LoaderModal';
import * as MyPlanSelectionCard from 'src/components/common/MyPlanSelectionCard/MyPlanSelectionCard';
import * as Oops from 'src/components/common/Oops/Oops';
import * as SwitchHold from 'src/components/common/Oops/SwitchHold';
import * as PageBanner from 'src/components/common/PageBanner/PageBanner';
import * as PageBannerWithStepper from 'src/components/common/PageBannerWithStepper/PageBannerWithStepper';
import * as PageTitleAndDescription from 'src/components/common/PageTitleAndDescription/PageTitleAndDescription';
import * as PrintSelectedPlanCard from 'src/components/common/PrintSelectedPlanCard/PrintSelectedPlanCard';
import * as ProgressBar from 'src/components/common/ProgressBar/ProgressBar';
import * as ProgressBarMobile from 'src/components/common/ProgressBarMobile/ProgressBarMobile';
import * as RatingAndReviews from 'src/components/common/RatingAndReviews/RatingAndReviews';
import * as Ratings from 'src/components/common/Ratings/Ratings';
import * as Events from 'src/components/common/RVEvent/Events';
import * as SalesforceLiveChat from 'src/components/common/SalesforceLiveChat/SalesforceLiveChat';
import * as SelectedPlanCard from 'src/components/common/SelectedPlanCard/SelectedPlanCard';
import * as SelectedPlanCardContainer from 'src/components/common/SelectedPlanCardContainer/SelectedPlanCardContainer';
import * as SelectedPlanCardWithDetails from 'src/components/common/SelectedPlanCardWithDetails/SelectedPlanCardWithDetails';
import * as SeventyThirtyContainer from 'src/components/common/SeventyThirtyContainer/SeventyThirtyContainer';
import * as SideBarImageCard from 'src/components/common/SideBarImageCard/SideBarImageCard';
import * as Stepper from 'src/components/common/Stepper/Stepper';
import * as SwitchToMoveInPopup from 'src/components/common/SwitchToMoveInPopup/SwitchToMoveInPopup';
import * as TEEFooter from 'src/components/common/TEEFooter/TEEFooter';
import * as TEEHeader from 'src/components/common/TEEHeader/TEEHeader';
import * as TEEShoppingMenu from 'src/components/common/TEEShoppingMenu/TEEShoppingMenu';
import * as TXUFooter from 'src/components/common/TXUFooter/TXUFooter';
import * as TXUHeader from 'src/components/common/TXUHeader/TXUHeader';
import * as TXUTopHeader from 'src/components/common/TXUTopHeader/TXUTopHeader';
import * as UpdateBanner from 'src/components/common/UpdateBanner/UpdateBanner';
import * as XRouteCookie from 'src/components/common/XRouteCookie/XRouteCookie';
import * as YesNoButton from 'src/components/common/YesNoButton/YesNoButton';
import * as EnrollmentConfirmation from 'src/components/Confirmation/EnrollmentConfirmation/EnrollmentConfirmation';
import * as EnrollmentConfirmationContainer from 'src/components/Confirmation/EnrollmentConfirmationContainer/EnrollmentConfirmationContainer';
import * as RentionConfirmationContainer from 'src/components/Confirmation/RentionConfirmationContainer/RentionConfirmationContainer';
import * as ContentBlock from 'src/components/ContentBlock/ContentBlock';
import * as Button from 'src/components/Elements/Button/Button';
import * as ExternalLink from 'src/components/Elements/ExternalLink/ExternalLink';
import * as Incentive from 'src/components/Elements/Incentive/Incentive';
import * as Input from 'src/components/Elements/Input/Input';
import * as Link from 'src/components/Elements/Link/Link';
import * as MultiSwitch from 'src/components/Elements/MultiSwitch/MultiSwitch';
import * as Switch from 'src/components/Elements/MultiSwitch/Switch';
import * as PrintPageButton from 'src/components/Elements/PrintPageButton/PrintPageButton';
import * as Select from 'src/components/Elements/Select/Select';
import * as Tooltip from 'src/components/Elements/Tooltip/Tooltip';
import * as ErrorBoundary from 'src/components/ErrorBoundary/ErrorBoundary';
import * as ExitEnrollment from 'src/components/ExitEnrollment/ExitEnrollment';
import * as StyleguideFieldUsageCheckbox from 'src/components/fields/Styleguide-FieldUsage-Checkbox';
import * as StyleguideFieldUsageContentList from 'src/components/fields/Styleguide-FieldUsage-ContentList';
import * as StyleguideFieldUsageCustom from 'src/components/fields/Styleguide-FieldUsage-Custom';
import * as StyleguideFieldUsageDate from 'src/components/fields/Styleguide-FieldUsage-Date';
import * as StyleguideFieldUsageFile from 'src/components/fields/Styleguide-FieldUsage-File';
import * as StyleguideFieldUsageImage from 'src/components/fields/Styleguide-FieldUsage-Image';
import * as StyleguideFieldUsageItemLink from 'src/components/fields/Styleguide-FieldUsage-ItemLink';
import * as StyleguideFieldUsageLink from 'src/components/fields/Styleguide-FieldUsage-Link';
import * as StyleguideFieldUsageNumber from 'src/components/fields/Styleguide-FieldUsage-Number';
import * as StyleguideFieldUsageRichText from 'src/components/fields/Styleguide-FieldUsage-RichText';
import * as StyleguideFieldUsageText from 'src/components/fields/Styleguide-FieldUsage-Text';
const GraphQLConnectedDemo = {
  module: () => import('src/components/graphql/GraphQL-ConnectedDemo.dynamic'),
  element: (isEditing?: boolean) => isEditing ? require('src/components/graphql/GraphQL-ConnectedDemo.dynamic')?.default : dynamic(GraphQLConnectedDemo.module)
}
import * as GraphQLIntegratedDemo from 'src/components/graphql/GraphQL-IntegratedDemo';
import * as GraphQLLayout from 'src/components/graphql/GraphQL-Layout';
import * as HelpMeChoosePopup from 'src/components/HelpMeChoosePopup/HelpMeChoosePopup';
import * as HelpMeChooseQuestion from 'src/components/HelpMeChooseQuestion/HelpMeChooseQuestion';
import * as Interstitials from 'src/components/Interstitials/Interstitials';
import * as Loading from 'src/components/Loading/Loading';
import * as Maintenance from 'src/components/MaintenanceSettings/Maintenance';
import * as PaymentLocationMap from 'src/components/Maps/PaymentLocationMap/PaymentLocationMap';
import * as AutoPayBlock from 'src/components/MiniConfirmation/AutoPayBlock/AutoPayBlock';
import * as CreateAccount from 'src/components/MiniConfirmation/CreateAccount/CreateAccount';
import * as CreateAccountTEE from 'src/components/MiniConfirmation/CreateAccount/CreateAccountTEE';
import * as EVSelection from 'src/components/MiniConfirmation/EVSelection/EVSelection';
import * as MiniConfirmationContainer from 'src/components/MiniConfirmation/MiniConfirmationContainer/MiniConfirmationContainer';
import * as MiniConfirmationContainerTEE from 'src/components/MiniConfirmation/MiniConfirmationContainer/MiniConfirmationContainerTEE';
import * as PaperlessBilling from 'src/components/MiniConfirmation/PaperlessBilling/PaperlessBilling';
import * as WelcomeBlock from 'src/components/MiniConfirmation/WelcomeBlock/WelcomeBlock';
import * as AddAccountSetup from 'src/components/MyAccount/Add/AddAccountSetup/AddAccountSetup';
import * as AddBillingInfo from 'src/components/MyAccount/Add/AddBillingInfo/AddBillingInfo';
import * as AddConfirmation from 'src/components/MyAccount/Add/AddConfirmation/AddConfirmation';
import * as AddOrderInfoContainer from 'src/components/MyAccount/Add/AddOrderInfoContainer/AddOrderInfoContainer';
import * as AddServiceInfo from 'src/components/MyAccount/Add/AddServiceInfo/AddServiceInfo';
import * as AccountTopPanel from 'src/components/MyAccount/common/AccountTopPanel/AccountTopPanel';
import * as CurrentPlan from 'src/components/MyAccount/common/CurrentPlan/CurrentPlan';
import * as CurrentPlanContainer from 'src/components/MyAccount/common/CurrentPlanContainer/CurrentPlanContainer';
import * as DashBoardContainer from 'src/components/MyAccount/common/DashBoardContainer/DashBoardContainer';
import * as MyAccountPlaceOrderContainer from 'src/components/MyAccount/common/MyAccountPlaceOrderContainer/MyAccountPlaceOrderContainer';
import * as Login from 'src/components/MyAccount/Login/Login';
import * as Logout from 'src/components/MyAccount/Logout/Logout';
import * as MyAccountPlaceOrderDisclaimer from 'src/components/MyAccount/MyAccountPlaceOrderDisclaimer/MyAccountPlaceOrderDisclaimer';
import * as CompareNewHome from 'src/components/MyAccountViewPlans/CompareNewHome/CompareNewHome';
import * as MyAccountPlanCardList from 'src/components/MyAccountViewPlans/MyAccountPlanCardList/MyAccountPlanCardList';
import * as MyAccountTEEPlanCardList from 'src/components/MyAccountViewPlans/MyAccountTEEPlanCardList/MyAccountTEEPlanCardList';
import * as MyAccountViewPlans75_25Container from 'src/components/MyAccountViewPlans/MyAccountViewPlans75_25Container/MyAccountViewPlans75_25Container';
import * as MyAccountViewPlansContainer from 'src/components/MyAccountViewPlans/MyAccountViewPlansContainer/MyAccountViewPlansContainer';
import * as BillingInformation from 'src/components/OrderInformation/BillingInformation/BillingInformation';
import * as CustomerInformation from 'src/components/OrderInformation/CustomerInformation/CustomerInformation';
import * as DateSelector from 'src/components/OrderInformation/DateSelector/DateSelector';
import * as IdentityVerification from 'src/components/OrderInformation/IdentityVerification/IdentityVerification';
import * as OOWKIQ from 'src/components/OrderInformation/OOWKIQ/OOWKIQ';
import * as OOWVerifyOTP from 'src/components/OrderInformation/OOWVerifyOTP/OOWVerifyOTP';
import * as OrderInformationContainer from 'src/components/OrderInformation/OrderInformationContainer/OrderInformationContainer';
import * as ServiceInformation from 'src/components/OrderInformation/ServiceInformation/ServiceInformation';
import * as P2PContainer from 'src/components/P2P/P2PContainer/P2PContainer';
import * as PathToPlan from 'src/components/P2P/PathToPlan/PathToPlan';
import * as PageBuilder from 'src/components/PageBuilder/PageBuilder';
import * as Path2Plans from 'src/components/Path2Plans/Path2Plans';
import * as PaymentLocation from 'src/components/PaymentLocation/PaymentLocation';
import * as types from 'src/components/PaymentLocation/types';
import * as PaymetricIntegration from 'src/components/PaymetricIntegration/PaymetricIntegration';
import * as BalanceDue from 'src/components/PlaceOrder/BalanceDue/BalanceDue';
import * as DepositInfo from 'src/components/PlaceOrder/DepositInfo/DepositInfo';
import * as PaymentDetails from 'src/components/PlaceOrder/PaymentDetails/PaymentDetails';
import * as PlaceOrder from 'src/components/PlaceOrder/PlaceOrder/PlaceOrder';
import * as PlaceOrderAutoPay from 'src/components/PlaceOrder/PlaceOrderAutoPay/PlaceOrderAutoPay';
import * as PlaceOrderAutoPaytypes from 'src/components/PlaceOrder/PlaceOrderAutoPay/componentprops/PlaceOrderAutoPay.types';
import * as AutoPayCard from 'src/components/PlaceOrder/PlaceOrderAutoPay/elements/AutoPayCard';
import * as AutoPayDecisionFunction from 'src/components/PlaceOrder/PlaceOrderAutoPay/utils/AutoPayDecisionFunction';
import * as PlaceOrderContainer from 'src/components/PlaceOrder/PlaceOrderContainer/PlaceOrderContainer';
import * as PlaceOrderDepositContainer from 'src/components/PlaceOrder/PlaceOrderDepositContainer/PlaceOrderDepositContainer';
import * as PlaceOrderDisclaimer from 'src/components/PlaceOrder/PlaceOrderDisclaimer/PlaceOrderDisclaimer';
import * as PriorDebt from 'src/components/PlaceOrder/PriorDebt/PriorDebt';
import * as PriorDebtPayment from 'src/components/PlaceOrder/PriorDebtPayment/PriorDebtPayment';
import * as PriorDebtPaymentConfirmation from 'src/components/PlaceOrder/PriorDebtPaymentConfirmation/PriorDebtPaymentConfirmation';
import * as PriorDebtPaymentConfirmationContainer from 'src/components/PlaceOrder/PriorDebtPaymentConfirmationContainer/PriorDebtPaymentConfirmationContainer';
import * as PriorDebtTable from 'src/components/PlaceOrder/PriorDebtTable/PriorDebtTable';
import * as PriorDebtContainer from 'src/components/PlaceOrder/PriotDebtContainer/PriorDebtContainer';
import * as ExtendedPlanCard from 'src/components/PlanCard/ExtendedPlanCard';
import * as PlanCard from 'src/components/PlanCard/PlanCard';
import * as PlanCardListPopup from 'src/components/PlanCardListPopup/PlanCardListPopup';
import * as ReferAFriend from 'src/components/ReferAFriend/ReferAFriend';
import * as ComponentProps from 'src/components/ReferAFriend/componentprops/ComponentProps';
import * as referralFunction from 'src/components/ReferAFriend/functions/referralFunction';
import * as ChangeAddress from 'src/components/Renewal/ChangeAddress/ChangeAddress';
import * as ChangeAddressContainer from 'src/components/Renewal/ChangeAddressContainer/ChangeAddressContainer';
import * as CurrentPlanAndUsage from 'src/components/Renewal/CurrentPlanAndUsage/CurrentPlanAndUsage';
import * as RenewalConfirmation from 'src/components/Renewal/RenewalConfirmation/RenewalConfirmation';
import * as RenewalContainer from 'src/components/Renewal/RenewalContainer/RenewalContainer';
import * as NewPlanInformation from 'src/components/Renewal/RenewalOrderInfo/NewPlanInformation/NewPlanInformation';
import * as RenewalOrderInfoContainer from 'src/components/Renewal/RenewalOrderInfo/RenewalOrderInfoContainer/RenewalOrderInfoContainer';
import * as RenewalPersonalInfo from 'src/components/Renewal/RenewalOrderInfo/RenewalPersonalInfo/RenewalPersonalInfo';
import * as StyleguideComponentParams from 'src/components/styleguide/Styleguide-ComponentParams';
import * as StyleguideCustomRouteType from 'src/components/styleguide/Styleguide-CustomRouteType';
import * as StyleguideLayoutReuse from 'src/components/styleguide/Styleguide-Layout-Reuse';
import * as StyleguideLayoutTabsTab from 'src/components/styleguide/Styleguide-Layout-Tabs-Tab';
import * as StyleguideLayoutTabs from 'src/components/styleguide/Styleguide-Layout-Tabs';
import * as StyleguideLayout from 'src/components/styleguide/Styleguide-Layout';
import * as StyleguideMultilingual from 'src/components/styleguide/Styleguide-Multilingual';
import * as StyleguideRouteFields from 'src/components/styleguide/Styleguide-RouteFields';
import * as StyleguideSection from 'src/components/styleguide/Styleguide-Section';
import * as StyleguideSitecoreContext from 'src/components/styleguide/Styleguide-SitecoreContext';
import * as StyleguideSpecimen from 'src/components/styleguide/Styleguide-Specimen';
import * as StyleguideTracking from 'src/components/styleguide/Styleguide-Tracking';
import * as SunRunPlanCard from 'src/components/SunRunPlanCard/SunRunPlanCard';
import * as SunRunPlanCardList from 'src/components/SunRunPlanCardList/SunRunPlanCardList';
import * as SunRunPopup from 'src/components/SunRunPopup/SunRunPopup';
import * as NCPlanTermsText from 'src/components/TermsAndConditions/NCPlanTerms/NCPlanTermsText';
import * as TermsAndConditionsContainer from 'src/components/TermsAndConditions/TermsAndConditionsContainer/TermsAndConditionsContainer';
import * as TransferConfirmation from 'src/components/Transfer/TransferConfirmation/TransferConfirmation';
import * as TransferMultiMeter from 'src/components/Transfer/TransferHardStop/MultiMeter/TransferMultiMeter';
import * as TransferBillingInformation from 'src/components/Transfer/TransferOrderInfo/TransferBillingInformation/TransferBillingInformation';
import * as TransferOrderInfoContainer from 'src/components/Transfer/TransferOrderInfo/TransferOrderInfoContainer/TransferOrderInfoContainer';
import * as TransferPersonalInfo from 'src/components/Transfer/TransferOrderInfo/TransferPersonalInfo/TransferPersonalInfo';
import * as TransferSetServiceDate from 'src/components/Transfer/TransferOrderInfo/TransferSetServiceDate/TransferSetServiceDate';
import * as TransferServiceInfoContainer from 'src/components/Transfer/TransferServiceInfoContainer/TransferServiceInfoContainer';
import * as TransferServiceInformation from 'src/components/Transfer/TransferServiceInformation/TransferServiceInformation';
import * as AMBPlanCardList from 'src/components/ViewPlans/AMBPlanCardList/AMBPlanCardList';
import * as BrandBenefitCard from 'src/components/ViewPlans/BrandBenefitCard/BrandBenefitCard';
import * as BrandBenefits from 'src/components/ViewPlans/BrandBenefits/BrandBenefits';
import * as DocViewer from 'src/components/ViewPlans/DocViewer/DocViewer';
import * as EVRedirect from 'src/components/ViewPlans/EVRedirect/EVRedirect';
import * as EV from 'src/components/ViewPlans/EVVehicle/EV';
import * as EVVehicleOverlay from 'src/components/ViewPlans/EVVehicleOverlay/EVVehicleOverlay';
import * as FAQs from 'src/components/ViewPlans/FAQs/FAQs';
import * as QuestionsAndAnswers from 'src/components/ViewPlans/FAQs/QuestionsAndAnswers';
import * as GoogleReview from 'src/components/ViewPlans/GoogleReviews/GoogleReview';
import * as GoogleReviews from 'src/components/ViewPlans/GoogleReviews/GoogleReviews';
import * as HelpMeChoose from 'src/components/ViewPlans/HelpMeChoose/HelpMeChoose';
import * as PdfViewer from 'src/components/ViewPlans/PdfViewer/PdfViewer';
import * as PlanCardList from 'src/components/ViewPlans/PlanCardList/PlanCardList';
import * as PlanCardSort from 'src/components/ViewPlans/PlanCardSort/PlanCardSort';
import * as TEEPlanCardList from 'src/components/ViewPlans/TEEPlanCardList/TEEPlanCardList';
import * as UsageTool from 'src/components/ViewPlans/UsageTool/UsageTool';
import * as UsageToolAuth from 'src/components/ViewPlans/UsageToolAuth/UsageToolAuth';
import * as ViewPlansContainer from 'src/components/ViewPlans/ViewPlansContainer/ViewPlansContainer';
import * as MaxiWelcomeBlockWithPlanDetails from 'src/components/WelcomeBlock/MaxiWelcomeBlockWithPlanDetails/MaxiWelcomeBlockWithPlanDetails';
import * as WelcomeBlockWithPlanDetails from 'src/components/WelcomeBlockWithPlanDetails/WelcomeBlockWithPlanDetails';

export const components = new Map();
components.set('BYOCWrapper', BYOCWrapper);
components.set('FEaaSWrapper', FEaaSWrapper);

components.set('CdpPageView', CdpPageView);
components.set('ColumnSplitter', ColumnSplitter);
components.set('Container', Container);
components.set('FEAASScripts', FEAASScripts);
components.set('Image', Image);
components.set('LinkList', LinkList);
components.set('Navigation', Navigation);
components.set('PageContent', PageContent);
components.set('PartialDesignDynamicPlaceholder', PartialDesignDynamicPlaceholder);
components.set('Promo', Promo);
components.set('RichText', RichText);
components.set('RowSplitter', RowSplitter);
components.set('Title', Title);
components.set('AdditionalProducts', AdditionalProducts);
components.set('AddonPlanCard', AddonPlanCard);
components.set('ExtendedAddonPlanCard', ExtendedAddonPlanCard);
components.set('AddonPlanCardList', AddonPlanCardList);
components.set('AddressTypeAhead', AddressTypeAhead);
components.set('AMBPathToPlan', AMBPathToPlan);
components.set('AcquisitionContent', AcquisitionContent);
components.set('AdobeDataLayer', AdobeDataLayer);
components.set('AMBFooter', AMBFooter);
components.set('index', index);
components.set('AMBHeader', AMBHeader);
components.set('AuthenticationConfirmationContainer', AuthenticationConfirmationContainer);
components.set('BannerTitle', BannerTitle);
components.set('Calendar', Calendar);
components.set('Carousel', Carousel);
components.set('CharitySelection', CharitySelection);
components.set('CharitySelection.types', CharitySelectiontypes);
components.set('CharityCard', CharityCard);
components.set('GenesysChat', GenesysChat);
components.set('GenesysChatprops', GenesysChatprops);
components.set('FloatingButton', FloatingButton);
components.set('GenesysChatFloatingLink', GenesysChatFloatingLink);
components.set('GenesysChatToggleLink', GenesysChatToggleLink);
components.set('GenesysChatToggleLinkMobile', GenesysChatToggleLinkMobile);
components.set('ChatFunctions', ChatFunctions);
components.set('CommonContainerWithoutMenu', CommonContainerWithoutMenu);
components.set('CookieConsentBanner', CookieConsentBanner);
components.set('CustomerSupport', CustomerSupport);
components.set('CustomProgressBar', CustomProgressBar);
components.set('Disclaimers', Disclaimers);
components.set('Divider', Divider);
components.set('DynamicHTML', DynamicHTML);
components.set('EditableTextBox', EditableTextBox);
components.set('ExitPopup', ExitPopup);
components.set('FooterDisclaimer', FooterDisclaimer);
components.set('GlobalLinkClickListener', GlobalLinkClickListener);
components.set('InfoText', InfoText);
components.set('LanguageSelector', LanguageSelector);
components.set('LanguageSelectorMobile', LanguageSelectorMobile);
components.set('Avatar', Avatar);
components.set('LeftMenuContainer', LeftMenuContainer);
components.set('MenuList', MenuList);
components.set('MobileMenu', MobileMenu);
components.set('SiteLogo', SiteLogo);
components.set('LeftNavigation', LeftNavigation);
components.set('LoaderModal', LoaderModal);
components.set('MyPlanSelectionCard', MyPlanSelectionCard);
components.set('Oops', Oops);
components.set('SwitchHold', SwitchHold);
components.set('PageBanner', PageBanner);
components.set('PageBannerWithStepper', PageBannerWithStepper);
components.set('PageTitleAndDescription', PageTitleAndDescription);
components.set('PrintSelectedPlanCard', PrintSelectedPlanCard);
components.set('ProgressBar', ProgressBar);
components.set('ProgressBarMobile', ProgressBarMobile);
components.set('RatingAndReviews', RatingAndReviews);
components.set('Ratings', Ratings);
components.set('Events', Events);
components.set('SalesforceLiveChat', SalesforceLiveChat);
components.set('SelectedPlanCard', SelectedPlanCard);
components.set('SelectedPlanCardContainer', SelectedPlanCardContainer);
components.set('SelectedPlanCardWithDetails', SelectedPlanCardWithDetails);
components.set('SeventyThirtyContainer', SeventyThirtyContainer);
components.set('SideBarImageCard', SideBarImageCard);
components.set('Stepper', Stepper);
components.set('SwitchToMoveInPopup', SwitchToMoveInPopup);
components.set('TEEFooter', TEEFooter);
components.set('TEEHeader', TEEHeader);
components.set('TEEShoppingMenu', TEEShoppingMenu);
components.set('TXUFooter', TXUFooter);
components.set('TXUHeader', TXUHeader);
components.set('TXUTopHeader', TXUTopHeader);
components.set('UpdateBanner', UpdateBanner);
components.set('XRouteCookie', XRouteCookie);
components.set('YesNoButton', YesNoButton);
components.set('EnrollmentConfirmation', EnrollmentConfirmation);
components.set('EnrollmentConfirmationContainer', EnrollmentConfirmationContainer);
components.set('RentionConfirmationContainer', RentionConfirmationContainer);
components.set('ContentBlock', ContentBlock);
components.set('Button', Button);
components.set('ExternalLink', ExternalLink);
components.set('Incentive', Incentive);
components.set('Input', Input);
components.set('Link', Link);
components.set('MultiSwitch', MultiSwitch);
components.set('Switch', Switch);
components.set('PrintPageButton', PrintPageButton);
components.set('Select', Select);
components.set('Tooltip', Tooltip);
components.set('ErrorBoundary', ErrorBoundary);
components.set('ExitEnrollment', ExitEnrollment);
components.set('Styleguide-FieldUsage-Checkbox', StyleguideFieldUsageCheckbox);
components.set('Styleguide-FieldUsage-ContentList', StyleguideFieldUsageContentList);
components.set('Styleguide-FieldUsage-Custom', StyleguideFieldUsageCustom);
components.set('Styleguide-FieldUsage-Date', StyleguideFieldUsageDate);
components.set('Styleguide-FieldUsage-File', StyleguideFieldUsageFile);
components.set('Styleguide-FieldUsage-Image', StyleguideFieldUsageImage);
components.set('Styleguide-FieldUsage-ItemLink', StyleguideFieldUsageItemLink);
components.set('Styleguide-FieldUsage-Link', StyleguideFieldUsageLink);
components.set('Styleguide-FieldUsage-Number', StyleguideFieldUsageNumber);
components.set('Styleguide-FieldUsage-RichText', StyleguideFieldUsageRichText);
components.set('Styleguide-FieldUsage-Text', StyleguideFieldUsageText);
components.set('GraphQL-ConnectedDemo', GraphQLConnectedDemo);
components.set('GraphQL-IntegratedDemo', GraphQLIntegratedDemo);
components.set('GraphQL-Layout', GraphQLLayout);
components.set('HelpMeChoosePopup', HelpMeChoosePopup);
components.set('HelpMeChooseQuestion', HelpMeChooseQuestion);
components.set('Interstitials', Interstitials);
components.set('Loading', Loading);
components.set('Maintenance', Maintenance);
components.set('PaymentLocationMap', PaymentLocationMap);
components.set('AutoPayBlock', AutoPayBlock);
components.set('CreateAccount', CreateAccount);
components.set('CreateAccountTEE', CreateAccountTEE);
components.set('EVSelection', EVSelection);
components.set('MiniConfirmationContainer', MiniConfirmationContainer);
components.set('MiniConfirmationContainerTEE', MiniConfirmationContainerTEE);
components.set('PaperlessBilling', PaperlessBilling);
components.set('WelcomeBlock', WelcomeBlock);
components.set('AddAccountSetup', AddAccountSetup);
components.set('AddBillingInfo', AddBillingInfo);
components.set('AddConfirmation', AddConfirmation);
components.set('AddOrderInfoContainer', AddOrderInfoContainer);
components.set('AddServiceInfo', AddServiceInfo);
components.set('AccountTopPanel', AccountTopPanel);
components.set('CurrentPlan', CurrentPlan);
components.set('CurrentPlanContainer', CurrentPlanContainer);
components.set('DashBoardContainer', DashBoardContainer);
components.set('MyAccountPlaceOrderContainer', MyAccountPlaceOrderContainer);
components.set('Login', Login);
components.set('Logout', Logout);
components.set('MyAccountPlaceOrderDisclaimer', MyAccountPlaceOrderDisclaimer);
components.set('CompareNewHome', CompareNewHome);
components.set('MyAccountPlanCardList', MyAccountPlanCardList);
components.set('MyAccountTEEPlanCardList', MyAccountTEEPlanCardList);
components.set('MyAccountViewPlans75_25Container', MyAccountViewPlans75_25Container);
components.set('MyAccountViewPlansContainer', MyAccountViewPlansContainer);
components.set('BillingInformation', BillingInformation);
components.set('CustomerInformation', CustomerInformation);
components.set('DateSelector', DateSelector);
components.set('IdentityVerification', IdentityVerification);
components.set('OOWKIQ', OOWKIQ);
components.set('OOWVerifyOTP', OOWVerifyOTP);
components.set('OrderInformationContainer', OrderInformationContainer);
components.set('ServiceInformation', ServiceInformation);
components.set('P2PContainer', P2PContainer);
components.set('PathToPlan', PathToPlan);
components.set('PageBuilder', PageBuilder);
components.set('Path2Plans', Path2Plans);
components.set('PaymentLocation', PaymentLocation);
components.set('types', types);
components.set('PaymetricIntegration', PaymetricIntegration);
components.set('BalanceDue', BalanceDue);
components.set('DepositInfo', DepositInfo);
components.set('PaymentDetails', PaymentDetails);
components.set('PlaceOrder', PlaceOrder);
components.set('PlaceOrderAutoPay', PlaceOrderAutoPay);
components.set('PlaceOrderAutoPay.types', PlaceOrderAutoPaytypes);
components.set('AutoPayCard', AutoPayCard);
components.set('AutoPayDecisionFunction', AutoPayDecisionFunction);
components.set('PlaceOrderContainer', PlaceOrderContainer);
components.set('PlaceOrderDepositContainer', PlaceOrderDepositContainer);
components.set('PlaceOrderDisclaimer', PlaceOrderDisclaimer);
components.set('PriorDebt', PriorDebt);
components.set('PriorDebtPayment', PriorDebtPayment);
components.set('PriorDebtPaymentConfirmation', PriorDebtPaymentConfirmation);
components.set('PriorDebtPaymentConfirmationContainer', PriorDebtPaymentConfirmationContainer);
components.set('PriorDebtTable', PriorDebtTable);
components.set('PriorDebtContainer', PriorDebtContainer);
components.set('ExtendedPlanCard', ExtendedPlanCard);
components.set('PlanCard', PlanCard);
components.set('PlanCardListPopup', PlanCardListPopup);
components.set('ReferAFriend', ReferAFriend);
components.set('ComponentProps', ComponentProps);
components.set('referralFunction', referralFunction);
components.set('ChangeAddress', ChangeAddress);
components.set('ChangeAddressContainer', ChangeAddressContainer);
components.set('CurrentPlanAndUsage', CurrentPlanAndUsage);
components.set('RenewalConfirmation', RenewalConfirmation);
components.set('RenewalContainer', RenewalContainer);
components.set('NewPlanInformation', NewPlanInformation);
components.set('RenewalOrderInfoContainer', RenewalOrderInfoContainer);
components.set('RenewalPersonalInfo', RenewalPersonalInfo);
components.set('Styleguide-ComponentParams', StyleguideComponentParams);
components.set('Styleguide-CustomRouteType', StyleguideCustomRouteType);
components.set('Styleguide-Layout-Reuse', StyleguideLayoutReuse);
components.set('Styleguide-Layout-Tabs-Tab', StyleguideLayoutTabsTab);
components.set('Styleguide-Layout-Tabs', StyleguideLayoutTabs);
components.set('Styleguide-Layout', StyleguideLayout);
components.set('Styleguide-Multilingual', StyleguideMultilingual);
components.set('Styleguide-RouteFields', StyleguideRouteFields);
components.set('Styleguide-Section', StyleguideSection);
components.set('Styleguide-SitecoreContext', StyleguideSitecoreContext);
components.set('Styleguide-Specimen', StyleguideSpecimen);
components.set('Styleguide-Tracking', StyleguideTracking);
components.set('SunRunPlanCard', SunRunPlanCard);
components.set('SunRunPlanCardList', SunRunPlanCardList);
components.set('SunRunPopup', SunRunPopup);
components.set('NCPlanTermsText', NCPlanTermsText);
components.set('TermsAndConditionsContainer', TermsAndConditionsContainer);
components.set('TransferConfirmation', TransferConfirmation);
components.set('TransferMultiMeter', TransferMultiMeter);
components.set('TransferBillingInformation', TransferBillingInformation);
components.set('TransferOrderInfoContainer', TransferOrderInfoContainer);
components.set('TransferPersonalInfo', TransferPersonalInfo);
components.set('TransferSetServiceDate', TransferSetServiceDate);
components.set('TransferServiceInfoContainer', TransferServiceInfoContainer);
components.set('TransferServiceInformation', TransferServiceInformation);
components.set('AMBPlanCardList', AMBPlanCardList);
components.set('BrandBenefitCard', BrandBenefitCard);
components.set('BrandBenefits', BrandBenefits);
components.set('DocViewer', DocViewer);
components.set('EVRedirect', EVRedirect);
components.set('EV', EV);
components.set('EVVehicleOverlay', EVVehicleOverlay);
components.set('FAQs', FAQs);
components.set('QuestionsAndAnswers', QuestionsAndAnswers);
components.set('GoogleReview', GoogleReview);
components.set('GoogleReviews', GoogleReviews);
components.set('HelpMeChoose', HelpMeChoose);
components.set('PdfViewer', PdfViewer);
components.set('PlanCardList', PlanCardList);
components.set('PlanCardSort', PlanCardSort);
components.set('TEEPlanCardList', TEEPlanCardList);
components.set('UsageTool', UsageTool);
components.set('UsageToolAuth', UsageToolAuth);
components.set('ViewPlansContainer', ViewPlansContainer);
components.set('MaxiWelcomeBlockWithPlanDetails', MaxiWelcomeBlockWithPlanDetails);
components.set('WelcomeBlockWithPlanDetails', WelcomeBlockWithPlanDetails);

export const componentBuilder = new ComponentBuilder({ components });

export const moduleFactory = componentBuilder.getModuleFactory();
