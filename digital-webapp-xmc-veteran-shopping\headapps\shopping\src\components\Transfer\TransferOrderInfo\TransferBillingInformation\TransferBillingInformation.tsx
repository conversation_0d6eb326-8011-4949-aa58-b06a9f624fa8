import { Checkbox, Select, TextInput } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import {
  Text,
  Field,
  LinkField,
  RichText,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import { useEffect, useState } from 'react';
import { TransferOrderInfoFormType } from '../TransferOrderInfoContainer/TransferOrderInfoContainer';
import { useAppDispatch } from 'src/stores/store';
import axios from 'axios';
import { setTransferBillingInformation } from 'src/stores/transferSlice';
import { useRouter } from 'next/router';
import { Translation } from 'src/types/global';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown, faPenToSquare, faTimes } from '@fortawesome/pro-light-svg-icons';
import { camelCase } from 'src/utils/camelCase';
import PageBuilder from 'src/components/PageBuilder/PageBuilder';
import Pen from 'assets/icons/Pen';

type TransferBillingInformationProps = ComponentProps & {
  fields: {
    Header: Field<string>;
    DisclaimerText: Field<string>;
    EmailAddressText: Field<string>;
    BillingAddressText: Field<string>;
    TermsAndConditionLink: LinkField;
    TermsAndConditionLabel: Field<string>;
    SameAsServiceAddressCheckText: Field<string>;
    POBoxCheckText: Field<string>;
    POBoxText: Translation;
    StreetNumberText: Translation;
    StreetAddressText: Translation;
    AptOrUnitText: Translation;
    CityText: Translation;
    StateText: Translation;
    ZipcodeText: Translation;
    SecondaryAccountHolderCheckText: Field<string>;
    SecondaryAccountHolderTooltipText: Field<string>;
    SAHFirstNameText: Field<string>;
    SAHLastNameText: Field<string>;
    States: { displayName: string }[];
  };
  form: UseFormReturnType<TransferOrderInfoFormType>;
};

const TransferBillingInformation = (props: TransferBillingInformationProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  if (isPageEditing) return <PageBuilder componentName="Fields of TransferBillingInformation" />;
  const router = useRouter();
  let dispatch: ReturnType<typeof useAppDispatch>;
  if (!isPageEditing) {
    dispatch = useAppDispatch();
  }

  const [isTCChecked, setIsTCChecked] = useState(false);
  //const serviceInfo = useAppSelector((state) => state.transfer.personalInfo);
  const { contractaccount } = router.query;
  const [isSameAddress, setIsSameAddress] = useState(true);
  const [isPoBox, setIsPoBox] = useState(false);

  function editBillingAddress() {
    setIsSameAddress(false);
    props.form.setFieldValue('billingOption', 'differentAddress');
  }

  function showServiceAddress() {
    setIsSameAddress(true);
    props.form.setFieldValue('billingOption', 'sameAddress');
  }

  function handlePoBoxChange(event: React.ChangeEvent<HTMLInputElement>) {
    const isPoBoxChecked = event.currentTarget.checked;
    setIsPoBox(isPoBoxChecked);
    if (isPoBoxChecked) {
      props.form.setFieldValue('billingOption', 'poBox');
    } else {
      props.form.setFieldValue('billingOption', 'differentAddress');
    }
  }

  const GetPaperlessBilling = async () => {
    const response = await axios.get('/api/myaccount/paperlessbilling', {
      params: {
        ca: contractaccount,
      },
    });


    if (response.data && response.data?.result) {
      if (!isPageEditing) {
        // dispatch(
        //   setTransferBillingInformation({ isPaperlessEnabled: response.data.result.isPaperLess })
        // );
        dispatch(
          setTransferBillingInformation({ isPaperlessEnabled: false })
        );
      }
      // setIsTCChecked(response.data.result.isPaperLess);
      setIsTCChecked(false);
      props.form.setFieldValue('emailAddress', response.data.result.userEmail);
      // props.form.setFieldValue('enablePaperlessBilling', response.data.result.isPaperLess);
      props.form.setFieldValue('enablePaperlessBilling', false);
    }
  };

  useEffect(() => {
    GetPaperlessBilling();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="flex flex-col w-full max-w-[832px] sm:px-0 px-6 ipad:pl-[20px] wide:pl-[20px]">
      <hr className="h-px mb-8 border-borderVigintiternary border w-full px-4 max-w-[830px] m-auto wide:max-w-full ipad:max-w-full" />
      <div className="text-plus2 sm:text-plus2 text-textQuattuordenary font-primaryBold  mb-[20px]">
        {props.fields.Header.value}
      </div>
      <div>
        {/* <Checkbox
          checked={isTCChecked}
          {...props.form.getInputProps('enablePaperlessBilling')}
          onChange={(event) => {
            setIsTCChecked(event.currentTarget.checked);
            props.form.setFieldValue('enablePaperlessBilling', event.currentTarget.checked);
          }}
          styles={{
            body: {
              display: 'flex',
              alignItems: 'center',
            },
          }}
          radius="xs"
          label={
            <RichText
              field={{ value: props.fields.DisclaimerText.value }}
              tag="p"
              className="font-primaryRegular  text-[16px] text-textQuattuordenary tracking-normal leading-[20px] inline-link"
            />
          }
        /> */}
        {/* <div className="grid grid-cols-2 gap-4 my-4 max-w-[540px]">
          <Text
            field={props.fields.EmailAddressText}
            tag="span"
            className="text-textQuattuordenary text-base"
          />
          <div className="flex item-center">
            <Text
              tag="span"
              className="font-primaryRegular text-textQuattuordenary sm:break-words break-all"
              field={{ value: props.form.values.emailAddress.toLowerCase() }}
            />
          </div>
        </div> */}
        <div className="gap-4 my-4 max-w-[540px]">
          <Text
            field={props.fields.BillingAddressText}
            tag="span"
            className="text-textQuattuordenary text-base"
          />
          <div className="flex item-center">
            <Text
              tag="span"
              className="font-primaryRegular  text-textQuattuordenary sm:break-words break-all"
              field={{ value: camelCase(props.form.values.billingAddress) }}
            />

            <div className="hover:text-txublue ml-3 text-textSecondary hover:text-textPrimary cursor-pointer"
              onClick={() => {
                editBillingAddress();
              }}
            >
              <Pen />
            </div>
          </div>
          <div className="flex flex-col gap-6 w-[350px] mt-3">
            <div className="flex flex-row gap-8">
              {!isSameAddress && (
                <>
                  <Checkbox
                    radius={0}
                    size="xs"
                    checked={isPoBox}
                    onChange={handlePoBoxChange}
                    label={props.fields.POBoxCheckText.value}
                    styles={{
                      label: {
                        fontFamily: "OpenSans-Bold",
                        fontWeight: 500,
                        color: "#212529",
                        fontSize: "16px",
                      },
                    }}
                  />
                  <FontAwesomeIcon
                    icon={faTimes}
                    className="text-red-500 ml-2 cursor-pointer inline-block"
                    onClick={() => {
                      showServiceAddress();
                    }}
                    style={{ position: 'relative' }}
                  />
                </>
              )}
            </div>
            {!isSameAddress && !isPoBox && (
              <div className="flex flex-col gap-5 sm:gap-8">
                <div className="grid grid-cols-2 gap-5 sm:gap-6">
                  <TextInput
                    label={props.fields.StreetNumberText.fields.Message.value}
                    {...props.form.getInputProps('newBillingStreetNumber')}
                  />
                  <TextInput
                    label={props.fields.StreetAddressText.fields.Message.value}
                    {...props.form.getInputProps('newBillingStreetName')}
                  />
                  <TextInput
                    label={props.fields.AptOrUnitText.fields.Message.value}
                    {...props.form.getInputProps('newBillingUnitNumber')}
                  />
                  <TextInput
                    label={props.fields.CityText.fields.Message.value}
                    {...props.form.getInputProps('newBillingCity')}
                  />
                  <Select
                    styles={{
                      wrapper: {
                        [`@media (max-width: 767px)`]: {
                          width: '100%',
                        },
                      },
                      root: {
                        [`@media (max-width: 767px)`]: {
                          width: '100%',
                        },
                      },
                    }}
                    data={props.fields.States.map((state) => state.displayName)}
                    label="State"
                    {...props.form.getInputProps('newBillingState')}
                    rightSection={
                      <FontAwesomeIcon
                        icon={faChevronDown}
                        className="text-textSecondary hover:text-textPrimary"
                      />
                    }
                    selectOnBlur
                  />
                  <TextInput
                    label={props.fields.ZipcodeText.fields.Message.value}
                    {...props.form.getInputProps('newBillingPostalCode')}
                    styles={{
                      root: {
                        [`@media (max-width: 767px)`]: {
                          width: '100%',
                        },
                      },
                    }}
                  />
                </div>
              </div>
            )}
            {!isSameAddress && isPoBox && (
              <div className="flex flex-col gap-8">
                <div className="flex flex-row gap-6">
                  <TextInput
                    label={props.fields.POBoxText.fields.Message.value}
                    {...props.form.getInputProps('newBillingPOBox')}
                  />
                  <TextInput
                    label={props.fields.CityText.fields.Message.value}
                    {...props.form.getInputProps('newBillingPOBoxCity')}
                  />
                </div>
                <div className="flex flex-row gap-6">
                  <Select
                    styles={{
                      wrapper: {
                        width: '156px',
                      },
                    }}
                    data={props.fields.States.map((state) => state.displayName)}
                    label="State"
                    {...props.form.getInputProps('newBillingPOBoxState')}
                    rightSection={
                      <FontAwesomeIcon
                        icon={faChevronDown}
                        className="text-textSecondary hover:text-textPrimary"
                      />
                    }
                    selectOnBlur
                  />
                  <TextInput
                    label={props.fields.ZipcodeText.fields.Message.value}
                    {...props.form.getInputProps('newBillingPoBoxPostalCode')}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransferBillingInformation;
