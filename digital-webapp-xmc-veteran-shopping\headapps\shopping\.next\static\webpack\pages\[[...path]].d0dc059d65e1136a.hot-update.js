"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[[...path]]",{

/***/ "./src/components/Renewal/CurrentPlanAndUsage/CurrentPlanAndUsage.tsx":
/*!****************************************************************************!*\
  !*** ./src/components/Renewal/CurrentPlanAndUsage/CurrentPlanAndUsage.tsx ***!
  \****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CurrentPlanAndUsage: function() { return /* binding */ CurrentPlanAndUsage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss-nextjs */ \"./node_modules/@sitecore-jss/sitecore-jss-nextjs/dist/esm/index.js\");\n/* harmony import */ var src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/hoc/ApplicationInsightsLogger */ \"./src/hoc/ApplicationInsightsLogger.tsx\");\n/* harmony import */ var src_services_MyAccountAPI__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/services/MyAccountAPI */ \"./src/services/MyAccountAPI/index.ts\");\n/* harmony import */ var src_stores_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/stores/store */ \"./src/stores/store.ts\");\n/* harmony import */ var src_utils_authTokenUtil__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/utils/authTokenUtil */ \"./src/utils/authTokenUtil.ts\");\n\r\nvar _s = $RefreshSig$();\r\n\r\n\r\n\r\n\r\n\r\nconst CurrentPlanAndUsage = (props)=>{\r\n    _s();\r\n    console.log(\"props=\", props);\r\n    const context = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_5__.useSitecoreContext)();\r\n    const isPageEditing = context.sitecoreContext.pageEditing;\r\n    let plan = undefined;\r\n    if (!isPageEditing) {\r\n        plan = (0,src_stores_store__WEBPACK_IMPORTED_MODULE_3__.useAppSelector)((state)=>{\r\n            var _state_plans;\r\n            return (_state_plans = state.plans) === null || _state_plans === void 0 ? void 0 : _state_plans.KYCPlan;\r\n        });\r\n    }\r\n    const plandata = {\r\n        ContractNumber: \"\",\r\n        PlanName: \"\",\r\n        PlanID: \"\",\r\n        Price: 0,\r\n        Term: 0,\r\n        Usage: 0,\r\n        Term_Exp_Date: \"\",\r\n        Rate: {\r\n            AverageMonthlyUse: 0,\r\n            AveragePriceperkWh: 0\r\n        }\r\n    };\r\n    const myUsageData = {\r\n        esiid: \"\",\r\n        totalUsage: 0,\r\n        numOfMonths: 0,\r\n        avgMonthlyUsage: 0,\r\n        sqFootage: 0\r\n    };\r\n    const data = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_5__.useComponentProps)(props.rendering.uid);\r\n    if ((data === null || data === void 0 ? void 0 : data.plan) !== null && (data === null || data === void 0 ? void 0 : data.plan) !== undefined) {\r\n        plandata.ContractNumber = data.plan.result.CONTRACT_NO;\r\n        plandata.PlanID = data.plan.result.PRODUCT_ID;\r\n        plandata.PlanName = data.plan.result.PRODUCT;\r\n        plandata.Price = parseFloat(data.plan.result.PRICE);\r\n        plandata.Term = data.plan.result.TERMMONTHCOUNT;\r\n        plandata.Usage = parseInt(data.plan.result.USAGE);\r\n        plandata.Term_Exp_Date = data.plan.result.TERM_EXP_DATE;\r\n        plandata.Rate.AverageMonthlyUse = parseInt(data.plan.result.USAGE);\r\n        plandata.Rate.AveragePriceperkWh = parseFloat(data.plan.result.PRICE);\r\n    } else {\r\n        plandata.ContractNumber = plan === null || plan === void 0 ? void 0 : plan.CONTRACT_NO;\r\n        plandata.PlanID = plan === null || plan === void 0 ? void 0 : plan.PRODUCT_ID;\r\n        plandata.PlanName = plan === null || plan === void 0 ? void 0 : plan.PRODUCT;\r\n        plandata.Price = parseFloat(plan === null || plan === void 0 ? void 0 : plan.PRICE);\r\n        plandata.Term = plan === null || plan === void 0 ? void 0 : plan.TERMMONTHCOUNT;\r\n        plandata.Usage = parseInt(plan === null || plan === void 0 ? void 0 : plan.USAGE);\r\n        plandata.Term_Exp_Date = plan === null || plan === void 0 ? void 0 : plan.TERM_EXP_DATE;\r\n        plandata.Rate.AverageMonthlyUse = parseInt(plan === null || plan === void 0 ? void 0 : plan.USAGE);\r\n        plandata.Rate.AveragePriceperkWh = parseFloat(plan === null || plan === void 0 ? void 0 : plan.PRICE);\r\n    }\r\n    if ((data === null || data === void 0 ? void 0 : data.usageOverview) !== null && (data === null || data === void 0 ? void 0 : data.usageOverview) !== undefined && (data === null || data === void 0 ? void 0 : data.usageOverview.result.length) > 0) {\r\n        myUsageData.esiid = data === null || data === void 0 ? void 0 : data.usageOverview.result[0].esiid;\r\n        myUsageData.totalUsage = data === null || data === void 0 ? void 0 : data.usageOverview.result[0].totalUsage;\r\n        myUsageData.numOfMonths = data === null || data === void 0 ? void 0 : data.usageOverview.result[0].numOfMonths;\r\n        myUsageData.avgMonthlyUsage = data === null || data === void 0 ? void 0 : data.usageOverview.result[0].avgMonthlyUsage;\r\n        myUsageData.sqFootage = data === null || data === void 0 ? void 0 : data.usageOverview.result[0].sqFootage;\r\n    }\r\n    console.log(\"plandata=\", plandata);\r\n    console.log(\"data=\", data);\r\n    console.log(\"plan=\", plan);\r\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n        className: \"w-full my-5 md:items-start pl-5 pr-5 md:mt-[30px] mt-[30px] ml-0 sm:pl-0 sm:ml-[410px] wide:ml-0 ipad:ml-0 ipad:pl-6 wide:pl-6\",\r\n        children: [\r\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_5__.Text, {\r\n                tag: \"p\",\r\n                className: \"font-primaryBlack text-xl text-center sm:text-plus3  sm:text-left text-textQuattuordenary\",\r\n                field: props.fields.CurrentPlanAndUsageTitle\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\Renewal\\\\CurrentPlanAndUsage\\\\CurrentPlanAndUsage.tsx\",\r\n                lineNumber: 119,\r\n                columnNumber: 7\r\n            }, undefined),\r\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                className: \"sm:flex sm:flex-row text-center sm:text-left gap-1 items-center md:items-start pt-5\",\r\n                children: [\r\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_5__.Text, {\r\n                        tag: \"p\",\r\n                        className: \"font-primaryRegular text-textQuattuordenary text-minus2 font-bold\",\r\n                        field: {\r\n                            value: \"\".concat(plandata.PlanName)\r\n                        }\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\Renewal\\\\CurrentPlanAndUsage\\\\CurrentPlanAndUsage.tsx\",\r\n                        lineNumber: 126,\r\n                        columnNumber: 9\r\n                    }, undefined),\r\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_5__.Text, {\r\n                        tag: \"p\",\r\n                        className: \"font-primaryRegular text-textQuattuordenary text-minus2\",\r\n                        field: {\r\n                            value: \"| \".concat(plandata.Term, \" Months - Fixed | \").concat(plandata.Price, \"\\xa2 per kWh\")\r\n                        }\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\Renewal\\\\CurrentPlanAndUsage\\\\CurrentPlanAndUsage.tsx\",\r\n                        lineNumber: 133,\r\n                        columnNumber: 9\r\n                    }, undefined),\r\n                    !props.fields.HidePlanUsageKWh.value && !isNaN(plandata.Usage) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_5__.Text, {\r\n                        tag: \"p\",\r\n                        className: \"font-primaryRegular text-textQuattuordenary text-minus2\",\r\n                        field: {\r\n                            value: \"at \".concat(plandata.Usage, \" kWh\")\r\n                        }\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\Renewal\\\\CurrentPlanAndUsage\\\\CurrentPlanAndUsage.tsx\",\r\n                        lineNumber: 141,\r\n                        columnNumber: 11\r\n                    }, undefined) : \"\"\r\n                ]\r\n            }, void 0, true, {\r\n                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\Renewal\\\\CurrentPlanAndUsage\\\\CurrentPlanAndUsage.tsx\",\r\n                lineNumber: 125,\r\n                columnNumber: 7\r\n            }, undefined),\r\n            !props.fields.HideAvgUsageKWh.value && (myUsageData === null || myUsageData === void 0 ? void 0 : myUsageData.avgMonthlyUsage) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                className: \"sm:flex sm:flex-row gap-1 pt-3 items-center text-center sm:text-left\",\r\n                children: [\r\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_5__.Text, {\r\n                        tag: \"p\",\r\n                        className: \"font-primaryBlack text-textPrimary text-2xl sm:text-[45px] font-bold\",\r\n                        field: {\r\n                            value: \"\".concat(myUsageData === null || myUsageData === void 0 ? void 0 : myUsageData.avgMonthlyUsage, \" kWh\")\r\n                        }\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\Renewal\\\\CurrentPlanAndUsage\\\\CurrentPlanAndUsage.tsx\",\r\n                        lineNumber: 154,\r\n                        columnNumber: 11\r\n                    }, undefined),\r\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_5__.Text, {\r\n                        tag: \"p\",\r\n                        className: \"font-primaryRegular text-textQuattuordenary text-sm  sm:pl-2\",\r\n                        field: props.fields.AverageMonthlyUsageTitle\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\Renewal\\\\CurrentPlanAndUsage\\\\CurrentPlanAndUsage.tsx\",\r\n                        lineNumber: 159,\r\n                        columnNumber: 11\r\n                    }, undefined)\r\n                ]\r\n            }, void 0, true, {\r\n                fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\Renewal\\\\CurrentPlanAndUsage\\\\CurrentPlanAndUsage.tsx\",\r\n                lineNumber: 153,\r\n                columnNumber: 9\r\n            }, undefined) : \"\"\r\n        ]\r\n    }, void 0, true, {\r\n        fileName: \"E:\\\\veteran_repo\\\\digital-webapp-xmc-veteran-shopping\\\\headapps\\\\shopping\\\\src\\\\components\\\\Renewal\\\\CurrentPlanAndUsage\\\\CurrentPlanAndUsage.tsx\",\r\n        lineNumber: 118,\r\n        columnNumber: 5\r\n    }, undefined);\r\n};\r\n_s(CurrentPlanAndUsage, \"y6IKw1MS67xdrweSGPuUaBonHog=\", false, function() {\r\n    return [\r\n        _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_5__.useSitecoreContext,\r\n        _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_5__.useComponentProps\r\n    ];\r\n});\r\n_c = CurrentPlanAndUsage;\r\n\r\nconst Component = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_5__.withDatasourceCheck)()(CurrentPlanAndUsage);\r\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(Component, Component.name));\r\nvar _c, _c1;\r\n$RefreshReg$(_c, \"CurrentPlanAndUsage\");\r\n$RefreshReg$(_c1, \"%default%\");\r\n\r\n\r\n// Wrapped in an IIFE to avoid polluting the global scope\r\n;\r\n(function () {\r\n    var _a, _b;\r\n    // Legacy CSS implementations will `eval` browser code in a Node.js context\r\n    // to extract CSS. For backwards compatibility, we need to check we're in a\r\n    // browser context before continuing.\r\n    if (typeof self !== 'undefined' &&\r\n        // AMP / No-JS mode does not inject these helpers:\r\n        '$RefreshHelpers$' in self) {\r\n        // @ts-ignore __webpack_module__ is global\r\n        var currentExports = module.exports;\r\n        // @ts-ignore __webpack_module__ is global\r\n        var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\r\n        // This cannot happen in MainTemplate because the exports mismatch between\r\n        // templating and execution.\r\n        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\r\n        // A module can be accepted automatically based on its exports, e.g. when\r\n        // it is a Refresh Boundary.\r\n        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\r\n            // Save the previous exports signature on update so we can compare the boundary\r\n            // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\r\n            module.hot.dispose(function (data) {\r\n                data.prevSignature =\r\n                    self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\r\n            });\r\n            // Unconditionally accept an update to this module, we'll check if it's\r\n            // still a Refresh Boundary later.\r\n            // @ts-ignore importMeta is replaced in the loader\r\n            module.hot.accept();\r\n            // This field is set when the previous version of this module was a\r\n            // Refresh Boundary, letting us know we need to check for invalidation or\r\n            // enqueue an update.\r\n            if (prevSignature !== null) {\r\n                // A boundary can become ineligible if its exports are incompatible\r\n                // with the previous exports.\r\n                //\r\n                // For example, if you add/remove/change exports, we'll want to\r\n                // re-execute the importing modules, and force those components to\r\n                // re-render. Similarly, if you convert a class component to a\r\n                // function, we want to invalidate the boundary.\r\n                if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\r\n                    module.hot.invalidate();\r\n                }\r\n                else {\r\n                    self.$RefreshHelpers$.scheduleUpdate();\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            // Since we just executed the code for the module, it's possible that the\r\n            // new exports made it ineligible for being a boundary.\r\n            // We only care about the case when we were _previously_ a boundary,\r\n            // because we already accepted this update (accidental side effect).\r\n            var isNoLongerABoundary = prevSignature !== null;\r\n            if (isNoLongerABoundary) {\r\n                module.hot.invalidate();\r\n            }\r\n        }\r\n    }\r\n})();\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Renewal/CurrentPlanAndUsage/CurrentPlanAndUsage.tsx\n"));

/***/ })

});