import { UseFormReturnType } from '@mantine/form';
import { Field, Text, useSitecoreContext } from '@sitecore-jss/sitecore-jss-nextjs';
import axios, { AxiosError } from 'axios-1.4';
import AddressTypeAhead from 'components/AddressTypeAhead/AddressTypeAhead';
import Calendar from 'components/common/Calendar/Calendar';
import { logErrorToAppInsights } from 'lib/app-insights-log-error';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';
import { GetConnectDate } from 'src/services/CalendarAPI/types';
import { useAppSelector } from 'src/stores/store';
import CalendarValidator from 'src/utils/calendarValidator';
import { CustomerIntent, QueryParamsMapType } from 'src/utils/query-params-mapping';
import { AddOrderInfoFormType } from '../AddOrderInfoContainer/AddOrderInfoContainer';
import dayjs from 'dayjs';

type AddServiceInfoProps = {
  fields: {
    Title: Field<string>;
    Holidays: Field<string>;
    ServiceAddressLable: Field<string>;
    ESIIDLable: Field<string>;
    CalendarDays: Field<string>;
    PriorityConnectionText: Field<string>;
    CalendarDisclaimer: Field<string>;
    ServiceStartText: Field<string>;
  };
  form: UseFormReturnType<AddOrderInfoFormType>;
};

const AddServiceInfo = (props: AddServiceInfoProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let selectedAddress = undefined;
  if (!isPageEditing) {
    selectedAddress = useAppSelector((state) => state.add?.selectedAddress);
  }
  const router = useRouter();
  const { cint } = router.query as QueryParamsMapType;
  const esiid = props.form.values.esiid;
  const [calendarData, setCalendarData] = useState<GetConnectDate | null>(null);

  const calendarValidator = useMemo(() => {
    if (calendarData) {
      const data = calendarData?.result;
      const newValidator = new CalendarValidator(
        data?.workDays,
        data?.holidays.concat(
          props.fields.Holidays ? props.fields?.Holidays?.value?.split(',') : []
        ),
        data?.priorityDays,
        data?.standardDays
      );
      return newValidator;
    } else return null;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [calendarData]);

  useEffect(() => {
    const fetchConnectDate = async () => {
      if (esiid) {
        try {
          const req = await axios.get<GetConnectDate>(
            `/api/calendar/addconnectdate?esiid=${esiid}&intent=${CustomerIntent[cint]}`
          );
          setCalendarData(req.data);
        } catch (err: unknown) {
          const error = err as AxiosError;
          logErrorToAppInsights(error, {
            componentStack: 'Add-ServiceInformation - fetchConnectDate',
          });
        }
      } else {
        props.form.setFieldValue('startdate', '');
      }
    };
    fetchConnectDate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [esiid, cint]);

  return (
    <div className="flex flex-col gap-6 sm:gap-3 mt-[25px] px-5 sm:px-0">
      <Text
        tag="p"
        className="text-textQuattuordenary font-primaryBold text-plus1 sm:text-plus2 py-2"
        field={{ value: props.fields?.Title?.value }}
      />
      <div className="w-full sm:w-[384px]">
        <AddressTypeAhead<AddOrderInfoFormType>
          form={props.form}
          formFields={{
            esiid: 'esiid',
            zip: 'zip',
            tdsp: 'tdsp',
            house_nbr: 'house_nbr',
            street: 'street',
            state: 'state',
            label: 'label',
            value: 'value',
            city: 'city',
          }}
          fullWidth
          error={props.form.errors.esiid}
          label="Service Address"
          serviceAddressLable={props.fields.ServiceAddressLable?.value}
          esiidLable={props.fields.ESIIDLable?.value}
          defaultData={selectedAddress}
          showInputDefault={false}
          editable={true}
          showUndoIcon={true}
          variant="Zip"
        />
      </div>
      <hr className="border-borderVigintiternary border-[1px] w-full sm:w-[636px] mt-[15px]" />
      {esiid && calendarData && calendarValidator && (
        <>
          <div className="mt-[15px]">
            {props.fields?.ServiceStartText?.value !== '' && (
              <span className="font-primaryBold text-plus1 sm:text-plus2 text-textQuattuordenary ">
                {props.fields?.ServiceStartText?.value}
              </span>
            )}
            <Text
              tag="p"
              field={{ value: props.fields?.SelectDateLabel?.value }}
              className=" font-primaryRegular text-minus1 sm:text-base text-textQuattuordenary mt-[20px] mb-[5px] "
            />
            <Calendar<AddOrderInfoFormType>
              form={props.form}
              formField="startdate"
              calendarData={calendarData}
              calendarValidator={calendarValidator}
              calendarDays={
                props.fields?.CalendarDays?.value !== 0 ? props.fields.CalendarDays?.value : 90
              }
              calendarDesclaimer={props.fields.CalendarDisclaimer?.value}
              error={props.form.errors.startdate}
              popoverMiddlewares={{ shift: false, flip: false }}
            />
            <hr className="border-borderVigintiternary border-[1px] w-full sm:w-[636px] mt-[30px]" />
          </div>
        </>
      )}
      {props.form.values.startdate &&
        calendarData &&
        calendarValidator &&
        calendarValidator.isPriorityDay(dayjs(props.form.values.startdate).toDate()) ? (
        <Text
          tag="p"
          className="text-cardinal mt-3"
          field={{
            value: props.fields.PriorityConnectionText?.value
              .replace('${date}', props.form.values.startdate)
              .replace('${priorityfee}', `$${calendarData.result.priorityFee.toString()}`),
          }}
        />
      ) : (
        <></>
      )}
    </div>
  );
};

export { AddServiceInfo };
// const Component = withDatasourceCheck()<AddServiceInfoProps>(AddServiceInfo);
// export default aiLogger(Component, Component.name);
export default AddServiceInfo;
