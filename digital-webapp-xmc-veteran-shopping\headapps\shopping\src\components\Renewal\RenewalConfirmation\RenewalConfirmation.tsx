import { useRef, useState } from 'react';
import { useRouter } from 'next/router';
import Button from 'components/Elements/Button/Button';
import InfoText from 'components/common/InfoText/InfoText';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import PrintPageButton from 'components/Elements/PrintPageButton/PrintPageButton';
import {
  Text,
  Field,
  LinkField,
  withDatasourceCheck,
  Placeholder,
  RichText,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import dayjs from 'dayjs';
import {
  OrderAddonProductRequest,
  OrderAddonProductResponse,
  OrderedAddonProduct,
} from 'src/services/AddOnProductAPI/types';
import { AxiosResponse } from 'axios';
import { DwellingType, QueryParamsMapType } from 'src/utils/query-params-mapping';
import axios, { AxiosError } from 'axios-1.4';
import { setOrderedPlans } from 'src/stores/planSlice';
import { logErrorToAppInsights } from 'lib/app-insights-log-error';
import { useNavigationDirection } from 'src/hooks/useNavigationDirection';
import PageBuilder from 'src/components/PageBuilder/PageBuilder';

type RenewalConfirmationProps = ComponentProps & {
  fields: {
    ConfirmationTitle: Field<string>;
    ConfirmationNumberLabel: Field<string>;
    AccountNumberLabel: Field<string>;
    PrintPageLabel: Field<string>;
    OrderInformationTitle: Field<string>;
    CurrentPlanLabel: Field<string>;
    CurrentESIIDLabel: Field<string>;
    NewPlanLabel: Field<string>;
    OrderInformationDescription: Field<string>;
    AccountSummaryButtonText: Field<string>;
    ResidentialMyAccountPageUrl: LinkField;
    CustomerNameLabel: Field<string>;
    ServiceAddressLabel: Field<string>;
    NewStartDateLabel: Field<string>;
    EffectiveNewPlanLabel: Field<string>;
    GreenUpProductText: Field<string>;
    OrderProductAndGoToAccountSummary: Field<string>;
    ServiceReceivedMessage: Field<string>;
  };
};

const RenewalConfirmation = (props: RenewalConfirmationProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  if (isPageEditing) return <PageBuilder componentName="Fields of RenewalConfirmation" />;
  useNavigationDirection();
  const router = useRouter();
  const { contractaccount, zip, esiid, planid, dwel } = router.query as QueryParamsMapType;
  const printRef = useRef<HTMLDivElement>(null);
  let dispatch: ReturnType<typeof useAppDispatch>;
  let planDetails = undefined;
  let renewalDetails = undefined;
  let authUser = undefined;
  if (!isPageEditing) {
    planDetails = useAppSelector((state) => state.plans);
    renewalDetails = useAppSelector((state) => state.renewal);
    authUser = useAppSelector((state) => state.authuser);
    dispatch = useAppDispatch();
  }
  const SelectedPlanRate = planDetails.selectedPlan.rate * 100;
  const KYCPlanPrice = parseFloat(planDetails.KYCPlan.PRICE);
  const [isOrderCompleted, setIsOrderCompleted] = useState(false);
  console.log('Selected Plan Term', planDetails.selectedPlan.term);

  async function redirectToNextPage() {
    if (props.fields?.ResidentialMyAccountPageUrl?.value?.href !== '') {
      const decodedUri = decodeURIComponent(
        props.fields.ResidentialMyAccountPageUrl?.value.href as string
      );
      router.push({
        pathname: decodedUri,
        query: {
          clearcache: true,
        },
      });
    }
  }

  const orderAddonProducts = async () => {
    try {
      //openModal();
      const addonPlanKeys = Object.keys(planDetails.addonPlans);

      if (addonPlanKeys && addonPlanKeys.length !== 0) {
        const selectedAddons: OrderedAddonProduct[] = addonPlanKeys.map((key) => {
          return {
            productId: planDetails.addonPlans[key].planId,
            quantity: 1,
          };
        });

        const orderAddonReq = await axios.post<
          OrderAddonProductResponse,
          AxiosResponse<OrderAddonProductResponse>,
          OrderAddonProductRequest
        >(
          '/api/myaccount/plans/addonplans',
          {
            businessPartnerNumber: authUser.bpNumber,
            contractAccountNumber: contractaccount,
            esiid: esiid,
            zipCode: zip,
            currentPlanId: planid,
            dwellingType: DwellingType[dwel as string],
            language: 'English',
            productOrders: selectedAddons,
          },
          { headers: { 'Content-Type': 'application/json' } }
        );
        setIsOrderCompleted(true);
        if (!isPageEditing) {
          dispatch(setOrderedPlans(orderAddonReq.data.result));
        }
        redirectToNextPage();
      }
      redirectToNextPage();
    } catch (err) {
      const error = err as AxiosError;
      logErrorToAppInsights(error, {
        componentStack: 'My Account Place Order Swap - orderAddonProducts',
      });
      const queryParams = {
        errorCode: 'My Account Place Order Swap - orderAddonProducts',
      };
      router.push({
        pathname: '/oops',
        query: { ...queryParams },
      });
    }
  };
  return (
    <div className="flex flex-col gap-8 sm:px-0 md:gap-10 w-full md:w-[864px] wide:w-full ipad:w-full ipad:px-[20px] wide:px-[20px] ">
      <div className="enrollment-confirmation print:m-8 print:w-11/12" ref={printRef}>
        <div className="flex flex-col p-5 gap-5 md:gap-8 px-0 ">
          <div className="flex sm:flex-row flex-col md:items-center">
            <Text
              tag="p"
              className="font-primaryBlack text-textQuattuordenary text-plus1 md:flex-grow md:text-plus3 break-words sm:max-w-[630px] sm:pr-4"
              field={props.fields.ConfirmationTitle}
            />
            <div className='mt-2 sm:mt-0 mr-auto sm:ml-auto'>
              <PrintPageButton label={props.fields.PrintPageLabel.value} printRef={printRef} /></div>
          </div>
          <Text
            tag="p"
            className="font-primaryRegular "
            field={props.fields.ServiceReceivedMessage}
          />
          <div className="flex flex-col gap-2 md:gap-3">
            <InfoText
              label={props.fields.ConfirmationNumberLabel.value}
              value={renewalDetails.renewalInfo.serviceContractNumber}
            />
            <InfoText
              label={props.fields.AccountNumberLabel.value}
              value={contractaccount as string}
            />
          </div>
          <div className="flex flex-row md:items-center">
            <Text
              tag="p"
              className="font-primaryBlack text-textQuattuordenary text-plus1 w-[170px] md:flex-grow md:text-plus3"
              field={props.fields.OrderInformationTitle}
            />
          </div>
        </div>
        <div>
          <InfoText
            label={props.fields.CustomerNameLabel.value}
            value={authUser?.userFirstName + ' ' + authUser?.userLastName}
          />
          <InfoText
            label={props.fields.ServiceAddressLabel.value}
            value={renewalDetails.renewalInfo.serviceAddress}
          />
          <div className="flex flex-row w-full py-4">
            <p className="font-primaryBold text-textQuattuordenary text-minus2 break-words w-[165px] md:w-[200px]">
              {props.fields.CurrentPlanLabel.value}:
            </p>
            <div className="flex flex-col">
              <span className="font-primaryRegular text-textQuattuordenary text-minus2">
                {planDetails?.KYCPlan.PRODUCT}
              </span>
              <span className="font-primaryRegular text-textQuattuordenary text-minus2">
                {planDetails?.KYCPlan?.TERMMONTHCOUNT === 0
                  ? 'Month-to-Month'
                  : planDetails?.KYCPlan.TERMMONTHCOUNT + ' Months - Fixed'}
              </span>
              {!isNaN(KYCPlanPrice) && (
                <span className="font-primaryRegular text-textQuattuordenary text-minus2">
                  {KYCPlanPrice + '¢' + ' per kWh'}
                </span>
              )}
            </div>
          </div>
          <InfoText
            label={props.fields.NewStartDateLabel.value}
            value={dayjs(renewalDetails?.renewalInfo.startDate).format('MM/DD/YYYY').toString()}
          />

          <InfoText label={props.fields.CurrentESIIDLabel.value} value={esiid as string} />
          <div className="flex flex-row w-full py-2">
            <p className="font-primaryBold text-textQuattuordenary text-minus2 break-words w-[165px] md:w-[200px]">
              {props.fields.NewPlanLabel.value}:
            </p>
            <div className="flex flex-col">
              <span className="font-primaryRegular text-textQuattuordenary text-minus2">
                {planDetails?.selectedPlan.planName}
              </span>
              <span className="font-primaryRegular text-textQuattuordenary text-minus2">
                {planDetails?.selectedPlan?.term === 0
                  ? 'Month-to-Month'
                  : planDetails?.selectedPlan.term + ' Months - Fixed'}
              </span>
              {!isNaN(SelectedPlanRate) && (
                <span className="font-primaryRegular text-textQuattuordenary text-minus2">
                  {SelectedPlanRate.toFixed(1) + '¢' + ' per kWh'}
                </span>
              )}
            </div>
          </div>
        </div>
        <>
          <Text
            tag="p"
            className="font-primaryRegular text-textQuattuordenary text-minus2"
            field={props.fields.EffectiveNewPlanLabel}
          />
          {planDetails?.selectedPlan.totalGreenUp && (
            <>
              <hr className="border-t-1 border-borderQuattuordenary-50 border-solid w-full max-w-[800px] px-[15px] m-auto" />
              <Text
                tag="p"
                className="font-primaryRegular text-textQuattuordenar text-minus2 bg-bgSeptendenary pl-8 pt-5 pb-5"
                field={props.fields.GreenUpProductText}
              />
            </>
          )}
          {planDetails?.EVDisclaimer.isEVSelected && (
            <>
              <hr className="border-t-1 border-borderQuattuordenary-50 border-solid w-full max-w-[800px] px-[15px] m-auto" />
              <RichText
                tag="p"
                className="font-primaryRegular text-textQuattuordenary text-text-minus2 bg-bgSeptendenary pl-8 pb-5 pt-5"
                field={{ value: planDetails.EVDisclaimer.EVDisclaimerMessage }}
              />
            </>
          )}
        </>
      </div>
      <Placeholder
        rendering={props.rendering}
        name="jss-addonplans"
        render={(components) => {
          return <div className="">{components}</div>;
        }}
      />
      {!isOrderCompleted && (
        <div className="sm:m-0 m-auto">
          <Button className="whitespace-normal leading-[18px]" onClick={orderAddonProducts}>
            {Object.keys(planDetails?.addonPlans).length > 0
              ? `${props.fields.OrderProductAndGoToAccountSummary?.value}`
              : `${props.fields.AccountSummaryButtonText?.value}`}
          </Button>
        </div>
      )}
    </div>
  );
};

export { RenewalConfirmation };
const Component = withDatasourceCheck()<RenewalConfirmationProps>(RenewalConfirmation);
export default aiLogger(Component, Component.name);
