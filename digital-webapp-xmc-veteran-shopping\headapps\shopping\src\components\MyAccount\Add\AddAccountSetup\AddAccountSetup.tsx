import { Radio, Select } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import { Field, Text, useSitecoreContext } from '@sitecore-jss/sitecore-jss-nextjs';
import axios from 'axios-1.4';
import { useEffect, useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown } from '@fortawesome/pro-light-svg-icons';
import { ContractAccountResponse } from 'src/services/AccountSelectorAPI/types';
import { AddOrderInfoFormType } from '../AddOrderInfoContainer/AddOrderInfoContainer';
import { useAppSelector } from 'src/stores/store';
import { isTxu } from 'src/utils/util';

type AddAccountSetupProps = {
  fields: {
    Title: Field<string>;
    UseExistingAccountText: Field<string>;
    UseExistingAccountDescription: Field<string>;
    UseSeparateAccountText: Field<string>;
  };
  form: UseFormReturnType<AddOrderInfoFormType>;
};

const AddAccountSetup = (props: AddAccountSetupProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let bpNumber = undefined;
  if (!isPageEditing) {
    bpNumber = useAppSelector((state) => state.authuser?.bpNumber);
  }
  const [contractAccounts, setContractAccounts] = useState<string[]>([]);

  useEffect(() => {
    const activeContractaccounts: string[] = [];
    const accountselector = async () => {
      const accountSelectorRequest = {
        PartnerNumber: bpNumber,
        Status: 'Active',
        ContractAccount: '',
      };

      const cas = await axios.post<ContractAccountResponse>(
        `/api/accountselector/contractaccount`,
        accountSelectorRequest
      );

      if (cas.data != undefined && cas.data.result.length > 0) {
        cas.data.result.map((ca) => {
          if (ca.accountStatus === 'Active') {
            activeContractaccounts.push(ca.contractAccount);
          }
        });

        if (activeContractaccounts.length === 1) {
          props.form.setFieldValue('contractAccount', activeContractaccounts[0]);
        }

        setContractAccounts([...activeContractaccounts]);
      }
    };
    if (bpNumber) {
      accountselector();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bpNumber]);

  return (
    <div className="flex flex-col gap-8">
      <Text
        tag="p"
        className="text-base sm:text-plus2 font-primaryBlack text-textQuattuordenary"
        field={{ value: props.fields?.Title?.value }}
      />
      <Radio.Group
        {...props.form.getInputProps('isExistingBill')}
        styles={() => {
          if (!isTxu)
            return {
              root: {
                display: 'flex',
                flexDirection: 'column',
                gap: '18px',
                label: { fontFamily: 'OpenSans-Regular' },
              },
            };
          else
            return {
              root: { display: 'flex', flexDirection: 'column', gap: '18px' },
            };
        }}
      >
        {/* First radio button hidden */}
        {/* <Radio value="true" label={props.fields?.UseExistingAccountText?.value} />
        {props.form.values.isExistingBill === 'true' ? (
          <>
            <Select
              data={contractAccounts}
              {...props.form.getInputProps('contractAccount')}
              styles={{
                root: {
                  width: '280px',
                  ['@media (max-width: 640px)']: {
                    width: '100%',
                  },
                },
              }}
              rightSection={
                contractAccounts.length > 1 ? (
                  <FontAwesomeIcon
                    icon={faChevronDown}
                    className="text-digitalBlueBonnet text-textSecondary hover:text-textPrimary"
                  />
                ) : (
                  <></>
                )
              }
              rightSectionProps={{ style: { marginRight: '8px' } }}
              readOnly={contractAccounts.length <= 1}
            />
            <Text
              tag="p"
              className="text-minus2 text-textQuattuordenary font-primaryRegular "
              field={{ value: props.fields?.UseExistingAccountDescription?.value }}
            />
          </>
        ) : (
          <></>
        )} */}
        <Radio value="false" label={props.fields?.UseSeparateAccountText?.value} />
      </Radio.Group>
      <hr className="border-borderVigintiternary border-[1px] w-full sm:w-[636px]" />
    </div>
  );
};

export { AddAccountSetup };
// const Component = withDatasourceCheck()<AddAccountSetupProps>(AddAccountSetup);
// export default aiLogger(Component, Component.name);

export default AddAccountSetup;
