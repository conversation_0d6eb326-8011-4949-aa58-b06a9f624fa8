import { Field, RichText, useSitecoreContext } from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import { TransferServiceInfoFormType } from '../TransferServiceInfoContainer/TransferServiceInfoContainer';
import { Group, Loader, Select as MantineSelect, Radio } from '@mantine/core';
import AddressTypeAhead from 'components/AddressTypeAhead/AddressTypeAhead';
import { UseFormReturnType } from '@mantine/form';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown } from '@fortawesome/pro-light-svg-icons';
import { useEffect, useState } from 'react';
import axios from 'axios';
import { ContractAccountResponse, EsiidResponse } from 'src/services/AccountSelectorAPI/types';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import { setMultiMeter } from 'src/stores/transferSlice';
import { Translation } from 'src/types/global';
import { camelCase } from 'src/utils/camelCase';

type TransferServiceInformationProps = ComponentProps & {
  fields: {
    Title: Field<string>;
    Description: Field<string>;
    ContractAccountNumberDDTitle: Field<string>;
    CurrentServiceAddressDDTitle: Field<string>;
    NewServiceAddressTitle: Field<string>;
    NewServiceAddressDDTitle: Field<string>;
    IsApartmentText: Field<string>;
    YesButtonLabel: Translation;
    NoButtonLabel: Translation;
    DwellingType: Field<string>;
  };
  form: UseFormReturnType<TransferServiceInfoFormType>;
};

interface DDMap {
  label: string;
  value: string;
}

const TransferServiceInformation = (props: TransferServiceInformationProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  const [contractAccounts, setContractAccounts] = useState<string[]>([]);
  const [serviceAddress, setServiceAddress] = useState<DDMap[]>([]);
  const [isCASelected, setIsCASelected] = useState(false);
  let bpNumber = undefined;
  let dispatch: ReturnType<typeof useAppDispatch>;
  if (!isPageEditing) {
    bpNumber = useAppSelector((state) => state.authuser?.bpNumber);
    dispatch = useAppDispatch();
  }
  useEffect(() => {
    const activeContractAccounts: string[] = [];
    const accountselector = async () => {
      const accountSelectorRequest = {
        PartnerNumber: bpNumber,
        Status: 'Active',
        ContractAccount: '',
      };

      const cas = await axios.post<ContractAccountResponse>(
        `/api/accountselector/contractaccount`,
        accountSelectorRequest
      );

      if (cas.data != undefined && cas.data.result.length > 0) {
        cas.data.result.map((ca) => {
          if (ca.accountStatus === 'Active') {
            activeContractAccounts.push(ca.contractAccount);
          }
        });

        if (activeContractAccounts.length === 1) {
          props.form.setFieldValue('ContractAccountNumber', activeContractAccounts[0]);
          getServiceAddresses(activeContractAccounts[0]);
        }
        setContractAccounts([...activeContractAccounts]);
      }
    };

    if (bpNumber) accountselector();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bpNumber]);

  const getServiceAddresses = async (CA: string) => {
    const esiidSet = new Set<DDMap>();
    const esiidRequest = {
      Partner: bpNumber,
      AccountNumber: CA,
      CutOffDate: '2010/03/01',
      PageNumber: 1,
      SortColumn: 'AccountStatus',
      SortDir: 'desc',
      Channel: 'WEB',
    };

    const esiids = await axios.post<EsiidResponse>('/api/accountselector/esiids', esiidRequest);

    if (esiids.data.result != undefined && esiids.data.result.length > 0) {
      esiids.data.result.map((esiid) => {
        if (esiid.accountStatus === 'Active') {
          esiidSet.add({
            label: camelCase(esiid.serviceAddress),
            value: esiid.esiId,
          });
        }
      });

      setServiceAddress([...esiidSet]);
      const activeESIIDs = esiids.data.result.filter((ele) => {
        return ele.accountStatus === 'Active';
      });
      props.form.setFieldValue('CurrentServiceAddressNumber', activeESIIDs[0]?.esiId);
      props.form.setFieldValue('CurrentServiceAddress', activeESIIDs[0]?.serviceAddress);

      //Setup for Multimeter
      const multiMeter = activeESIIDs.map((address) => address.serviceAddress);
      if (!isPageEditing) {
        dispatch(setMultiMeter({ esiids: multiMeter }));
      }
    }
  };

  const handleContractAccountChange = async (contractAccount: string) => {
    setIsCASelected(true);
    props.form.setFieldValue('ContractAccountNumber', contractAccount);
    props.form.setFieldValue('CurrentServiceAddressNumber', '');
    setServiceAddress([]);
    await getServiceAddresses(contractAccount);
  };

  const handleServiceAddressChange = async (currentServiceAddress: string) => {
    props.form.setFieldValue('CurrentServiceAddressNumber', currentServiceAddress);
  };

  useEffect(() => {
    const dwellingType = props?.fields?.DwellingType?.value;
    if (dwellingType === '02') {
      props.form.setFieldValue('IsApartment', 'yes');
    } else if (dwellingType === '01') {
      props.form.setFieldValue('IsApartment', 'no');
    } else {
      props.form.setFieldValue('IsApartment', '');
    }
  }, []);

  return (
    <div className="flex flex-col w-full max-w-[832px] sm:ml-[400px] my-8 mb-4 px-6 ipad:pl-6">
      <div>
        <RichText
          field={props.fields.Title}
          tag="p"
          className="text-textPrimary text-minus1 sm:text-plus2 font-primaryBlack text-center sm:text-left"
        />
        <RichText
          field={props.fields.Description}
          tag="p"
          className="text-textQuattuordenary text-minus2 font-primaryRegular sm:text-lg sm:text-left my-6"
        />
        <div className="sm:flex flex-col gap-8">
          <MantineSelect
            sx={{
              width: '384px',
              ['@media (max-width: 767px)']: {
                width: '100%',
                marginBottom: '25px'
              },
            }}
            classNames={{
              label: 'mb-2',
            }}
            {...props.form.getInputProps('ContractAccountNumber')}
            label={props.fields.ContractAccountNumberDDTitle.value}
            placeholder={
              contractAccounts.length === 0
                ? 'No Active CA found'
                : 'Please select Contract Account Number'
            }
            data={contractAccounts}
            styles={{
              rightSection: { pointerEvents: 'none' },
            }}
            rightSection={
              contractAccounts.length > 0 ? (
                <FontAwesomeIcon
                  icon={faChevronDown}
                  className={`text-textPrimary hover:text-textSecondary ${contractAccounts.length > 1 ? 'visible' : 'invisible'
                    }`}
                />
              ) : (
                contractAccounts.length !== 0 && <Loader size="sm" />
              )
            }
            withAsterisk
            onChange={handleContractAccountChange}
            readOnly={contractAccounts && contractAccounts.length === 1}
          // defaultValue={contractAccounts[0].label as string}
          />
          <MantineSelect
            sx={{
              width: '384px',
              ['@media (max-width: 767px)']: {
                width: '100%',
              },
            }}
            classNames={{
              label: 'mb-2',
            }}
            {...props.form.getInputProps('CurrentServiceAddressNumber')}
            label={props.fields.CurrentServiceAddressDDTitle.value}
            placeholder="Please select Service Address"
            data={serviceAddress}
            styles={{
              rightSection: { pointerEvents: 'none' },
            }}
            rightSection={
              serviceAddress.length > 0 ? (
                <FontAwesomeIcon
                  icon={faChevronDown}
                  className={`text-textPrimary hover:text-textSecondary ${serviceAddress.length > 1 ? 'visible' : 'invisible'
                    }`}
                />
              ) : isCASelected ? (
                <Loader size="sm" />
              ) : (
                ''
              )
            }
            withAsterisk
            onChange={handleServiceAddressChange}
            readOnly={serviceAddress && serviceAddress.length === 1}
          />
        </div>
      </div>
      <hr className="h-px border-borderVigintiternary border w-full max-w-[830px] px-[15px] my-8" />
      <div>
        <RichText
          field={props.fields.NewServiceAddressTitle}
          tag="p"
          className="text-textPrimary text-minus1 sm:text-plus2 font-primaryBlack sm:text-left mb-4"
        />
        <div className="sm:flex flex-col gap-8">
          <div className="w-full max-w-[384px]">
            <AddressTypeAhead<TransferServiceInfoFormType>
              form={props.form}
              formFields={{
                esiid: 'esiid',
                zip: 'zip',
                tdsp: 'tdsp',
                house_nbr: 'house_nbr',
                street: 'street',
                state: 'state',
                label: 'label',
                value: 'value',
                city: 'city',
                display_text: 'display_text',
                NewServiceAddress: 'label',
              }}
              error={props.form.errors.NewServiceAddress}
              label={props.fields.NewServiceAddressDDTitle.value}
              serviceAddressLable=""
              esiidLable=""
              showSuccessIcon={true}
            />
          </div>
          {/*<div className="flex flex-col gap-[18px]">
            <Radio.Group
              {...props.form.getInputProps('IsApartment')}
              label={`${props.fields.IsApartmentText.value}*`}
            >
              <Group mt={5}>
                <Radio
                  value="yes"
                  label={props.fields.YesButtonLabel.fields.Message.value}
                  styles={{
                    root: {
                      marginTop: '5px',
                    },
                  }}
                />
                <Radio
                  value="no"
                  label={props.fields.NoButtonLabel.fields.Message.value}
                  styles={{
                    root: {
                      marginTop: '5px',
                    },
                  }}
                />
              </Group>
            </Radio.Group>
          </div>*/}
        </div>
        <hr className="h-px border-borderVigintiternary my-6 border w-full max-w-[830px] px-[15px]" />
      </div>
    </div>
  );
};

export default TransferServiceInformation;
