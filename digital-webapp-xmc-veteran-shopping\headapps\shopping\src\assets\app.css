@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'MyriadPro-Regular';
  src: url('../../public/fonts/MyriadPro-Regular.otf');
}

@font-face {
  font-family: 'MyriadPro-Black';
  src: url('../../public/fonts/MyriadPro-Black.otf');
}

@font-face {
  font-family: 'MyriadPro-Light';
  src: url('../../public/fonts/MyriadPro-Light.otf');
}

@font-face {
  font-family: 'MyriadPro-Bold';
  src: url('../../public/fonts/MyriadPro-Bold.otf');
}

@font-face {
  font-family: 'MyriadPro-Semibold';
  src: url('../../public/fonts/MyriadPro-Semibold.otf');
}

@font-face {
  font-family: 'GothaPro-Regular';
  src: url('../../public/fonts/GothaProReg.otf');
}

@font-face {
  font-family: 'GothaPro-Medium';
  src: url('../../public/fonts/GothaProMed.otf');
}

@font-face {
  font-family: 'GothaPro-Bold';
  src: url('../../public/fonts/GothaProBol.otf');
}

@font-face {
  font-family: 'GothaPro-Light';
  src: url('../../public/fonts/GothaProLig.otf');
}

@font-face {
  font-family: 'GothaPro-Black';
  src: url('../../public/fonts/GothaProBla.otf');
}

@font-face {
  font-family: 'OpenSans-Regular';
  src: url('../../public/fonts/OpenSans-Regular.ttf');
}

@font-face {
  font-family: 'OpenSans-Bold';
  src: url('../../public/fonts/OpenSans-Bold.ttf');
}

@font-face {
  font-family: 'EnergyBold';
  src: url('../../public/fonts/EnergyBold.otf');
}
@media print {
  body {
    margin-top: 5rem; /* Adds space at the top of each printed page */
  }

  .print-page-break {
    page-break-before: always;
    margin-top: 5rem; /* Add spacing at top of new pages */
  }

  /* Optional: Reduce top margin for first page only if needed */
  body:first-child {
    margin-top: 5rem;
  }
}
@layer base {
  p,
  span,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-primaryRegular;
  }
}

@layer components {
  .inline-link a {
    @apply underline underline-offset-2 decoration-textPrimary hover:text-hoverSecondary focus:outline-none font-primaryRegular text-textPrimary hover:decoration-hoverSecondary;
  }
  .text-with-link a {
    @apply text-textPrimary underline font-primaryBold;
  }
  .text-no-underline a {
    text-decoration: none !important;
    color: #326295;
    font-weight: bold;
  }
}

@layer utilities {
  .scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .scrollbar::-webkit-scrollbar:horizontal {
    display: none;
  }

  .scrollbar::-webkit-scrollbar-track {
    width: 8px;
    background-color: #d3d6dc;
    border-radius: 4px;
  }

  .scrollbar::-webkit-scrollbar-thumb {
    width: 8px;
    border-radius: 4px;
    background-color: #0075da;
  }

  .scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #0075da #d3d6dc;
  }
}

* {
  box-sizing: border-box;
}

body {
  overflow-x: hidden;
}

header {
  background-color: white;
}
body {
  background-image: linear-gradient(
    15deg,
    rgba(0, 37, 84, 1) 41%,
    rgb(30, 27, 88) 77%,
    rgb(92, 30, 167) 100%
  );
  background-size: 100% 120px;
  background-repeat: no-repeat;
  @media (max-width: 767px) {
    background-size: 100% 155px;
  }
  @media only screen and (max-height: 820px) and (max-width: 1180px) {
    background-size: 100% 196px;
  }
}
.breakSeparator {
  word-break: break-word;
}
/*
  Hides Sitecore editor markup,
  if you run the app in connected mode while the Sitecore cookies
  are set to edit mode.
*/
.scChromeData,
.scpm {
  display: none !important;
}

/*
  Styles for default JSS error components
*/
.sc-jss-editing-error,
.sc-jss-placeholder-error {
  padding: 1em;
  background-color: lightyellow;
}

/* 
  Style for default content block
*/

/*additional css*/

.list-disc-custom li:before {
  content: '';
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAAOCAMAAAD6xte7AAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAADqUExURf////v+/eX48sjv4tHy5vf9+/r+/NXz6YrdwU/MoWLRq7zs29bz6XHWsxC5fwC0dgm3e0fJnfz+/tv17G3UsQW1eTHDkP7///z+/fn9/Pr+/f7//uP38WbSrg65fgG0dxm7g4PbvfP8+bPp13fYtpniye369uj482DRqgG0dhe7g3PXtZ/jzDPDkQ+4fhy8hV7Qqef48xS6gQK0d3jYt+v69X7ZuQy4fQK1dxW6guf48h+9h3vZuJ7jyyvBjVrPpyG+iH3Zut327d/27oLbvCK+iH7autr169n063vZuR29huT48X/ZujbEk8+QGbkAAAABYktHRACIBR1IAAAACXBIWXMAABJ0AAASdAHeZh94AAAAB3RJTUUH5wgHCDQg5GVwZQAAAAFvck5UAc+id5oAAAC7SURBVBjTVc/pVoJgEAbgydLSjDdzmRZatKxcWcxAqFiKXe7/duTjVEfm5zM7USUOaodH9So1jk+ardNqVftMwnlnny66PQnoD4j48uqaBd3It3fA/cOQaPT4NH4W9vIqTYDprNgxXywVVSPSVH0FKG/rIv1uwNyolqVuTMD+kMWczy/A1B1HL8j1/HL0908ArAyjaAzCqCTiOHRRhhvG/HsWR54tyPYi/r+V/SQF0sTnvQc42+b5NvujHb6HF3v8PSSRAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDIzLTA4LTA3VDA4OjUxOjQ0KzAwOjAwdS8QugAAACV0RVh0ZGF0ZTptb2RpZnkAMjAyMy0wOC0wN1QwODo1MTo0NCswMDowMARyqAYAAAAodEVYdGRhdGU6dGltZXN0YW1wADIwMjMtMDgtMDdUMDg6NTI6MzIrMDA6MDDRRQ75AAAAAElFTkSuQmCC');
  background-size: 10px 9px;
  position: absolute;
  left: -30px;
  top: 2px;
  width: 20px;
  height: 20px;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
}
.check_list ul li::before {
  content: '';
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAAOCAMAAAD6xte7AAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAADqUExURf////v+/eX48sjv4tHy5vf9+/r+/NXz6YrdwU/MoWLRq7zs29bz6XHWsxC5fwC0dgm3e0fJnfz+/tv17G3UsQW1eTHDkP7///z+/fn9/Pr+/f7//uP38WbSrg65fgG0dxm7g4PbvfP8+bPp13fYtpniye369uj482DRqgG0dhe7g3PXtZ/jzDPDkQ+4fhy8hV7Qqef48xS6gQK0d3jYt+v69X7ZuQy4fQK1dxW6guf48h+9h3vZuJ7jyyvBjVrPpyG+iH3Zut327d/27oLbvCK+iH7autr169n063vZuR29huT48X/ZujbEk8+QGbkAAAABYktHRACIBR1IAAAACXBIWXMAABJ0AAASdAHeZh94AAAAB3RJTUUH5wgHCDQg5GVwZQAAAAFvck5UAc+id5oAAAC7SURBVBjTVc/pVoJgEAbgydLSjDdzmRZatKxcWcxAqFiKXe7/duTjVEfm5zM7USUOaodH9So1jk+ardNqVftMwnlnny66PQnoD4j48uqaBd3It3fA/cOQaPT4NH4W9vIqTYDprNgxXywVVSPSVH0FKG/rIv1uwNyolqVuTMD+kMWczy/A1B1HL8j1/HL0908ArAyjaAzCqCTiOHRRhhvG/HsWR54tyPYi/r+V/SQF0sTnvQc42+b5NvujHb6HF3v8PSSRAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDIzLTA4LTA3VDA4OjUxOjQ0KzAwOjAwdS8QugAAACV0RVh0ZGF0ZTptb2RpZnkAMjAyMy0wOC0wN1QwODo1MTo0NCswMDowMARyqAYAAAAodEVYdGRhdGU6dGltZXN0YW1wADIwMjMtMDgtMDdUMDg6NTI6MzIrMDA6MDDRRQ75AAAAAElFTkSuQmCC');
  background-size: 10px 9px;
  position: absolute;
  left: -30px;
  top: 2px;
  width: 20px;
  height: 20px;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
  border: solid #03b578 !important;
  border-radius: 50% !important;
}
.list-disc-custom ul {
  margin-left: 30px;
}
.list-disc-custom li {
  position: relative;
  margin: 5px 0px;
}

.svg-top {
  position: relative;
  top: 0;
  margin-top: -27%;
  margin-bottom: -40px;
}
.w-fit .rounded-lg {
  background: #fff;
}

.ml-temp-14 {
  padding-left: 150px;
  padding-right: 50px;
}
.w-full .flex.flex-col.rounded-xl.font-primaryRegular.text-charcoal-full {
  background: #fff;
}
.plan-space {
  margin-top: 180px;
}

.close .close-icon {
  position: absolute;
  right: 32px;
  top: 32px;
  width: 32px;
  height: 32px;
  cursor: pointer;
  color: transparent;
}
.close .close-icon:hover {
  opacity: 1;
}
.close .close-icon:before,
.close .close-icon:after {
  position: absolute;
  left: 15px;
  content: ' ';
  height: 18px;
  width: 2.5px;
  background-color: #003f6e;
}
.close .close-icon:before {
  transform: rotate(45deg);
}
.close .close-icon:after {
  transform: rotate(-45deg);
}

.top-minus {
  top: -100px;
}

.embeddedServiceHelpButton .helpButton .uiButton {
  background-color: #005290 !important;
  font-family: 'Arial', sans-serif !important;
}

.embeddedServiceHelpButton .helpButton .uiButton:focus {
  outline: 1px solid #005290 !important;
}

.dialog-button-0 {
  display: none !important;
}
button.selected {
  background: #003f6e;
  color: white;
}
.tee button.selected {
  background: #297f9d;
  color: #e7ed67 !important;
}

@media (max-width: 767px) {
  .selected-tooltip,
  .usage-tooltip,
  .billing-tooltip,
  .extended-plan-tooltip {
    margin-left: 0;
  }
  .billing-tooltip {
    --tw-translate-x: -80%;
  }
  .paperlessbilling-tooltip .billing-tooltip {
    --tw-translate-x: -60%;
  }
  .usage-tooltip {
    --tw-translate-x: -60%;
  }
  .extended-plan-tooltip {
    --tw-translate-x: -60%;
  }
  .selected-tooltip {
    --tw-translate-x: -30%;
  }
  .addonplan-tooltip {
    --tw-translate-x: -80%;
  }
  .infoWindow {
    padding-left: 0px !important;
  }
  .table-custom-layout {
    display: grid !important;
    padding-bottom: 20px;
  }
  .show-badge + .show-plan {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
  }
  .selected-tooltip-mobile {
    --tw-translate-x: -70%;
  }
}
.my-account-logout:after {
  content: '';
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAbCAYAAABvCO8sAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAFiUAABYlAUlSJPAAAAH3SURBVEhLY/wPBAx0BExQmm6AIgsvPf/J8OMPaQFEtoVFm18z6Pc/YhBrvMfQtOcdVJQwIDsOGctuQ1kQ0OohzFDlJATl4QZk+zDBhA/KgoDqHW8Zug++h/JwA7ItLLEXZNAWZ4PyIKBs6xuGCYc/QHnYAdkWgixbGSOJYWkhMG6nHMNtKUWpFJeluRteM8w88RHKQwUUWQgCuCzNWPeKYe6pT1AeAlBsIQjgsjRlzUuGhWdRLaWKhSCAy9KElS8Zlp3/DOVB8+HyC58Z1lz6wvDu21+oMGXg9de/DFdf/oLyIADkmDA9HoiFYUueM6wGWkhLYCzDznAmTw4SpK+/UMdnxACwhfVuwgwOSpxgAVqBMgdIsUf1+jBg4XOGjVdRo2dppARDlCEvmE21VPrj938Gz7lPMSxbEC4OtwwEqGLh++//GLzmPWXYcfMbVAQC5oSIM8QboxbyFFv4/NMfBt/5zxj23/0OFYGAGUFiDMlmqJaBAEUW3n/3m8EfGGdHH6BaNjlAlCHdgh/KQwVkW3jj1S+GQKBlpx//gIpAQL+vKEOOlQCUhwnItrDzwHuGi8A2DTLo8hZhKLDFbRkIkG3hgjOohTKoiVEKrJQJAbItLETySSOw4CCmPQMCFGX8B+9/M/CxMzEIcTFDRQgDqpc0+AEDAwBhc7DRaOfs/gAAAABJRU5ErkJggg==');
  background-size: 20px 18px;
  position: relative;
  right: 0px;
  top: 10px;
  width: 30px;
  height: 30px;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
}
.my-account-logout:hover:after {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAZCAYAAAAiwE4nAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAFiUAABYlAUlSJPAAAAHQSURBVEhL1ZZLKERRHId/7O1tlIVsbJSSkkwNMfKaQl4xoijPESNMYSGhPEKNjLDwFpE8ihBKJO8oeaS80iCvxGK4c/65NAY515RvM+f738V375k7c6+N8RVYEVv6tBrcwfX9Ezw+PZP9AGFLf4u6vt8I7wyjnSLXWNI6StOv4foObWSZtGKUJgWiINaX7HO4tlTl704rRqF+GJVdk2SfwxXMiZTDxdGejKHRDaGmd5rMHK6gEOsuTjCLqhsGUD8wS/YR7rvUUjS9tg+NQ/NkItxBAUvRlKoeNI8skDEkCQpYiiZVdKJtfJFMwqCApaiqrB0dE8um9dvvsHNyGX0za7i8uTcd4OXi+g5bh2dkjO4ilRiMKG5B7/Sq6cBf4ebsIG6pcEbW4C1YpFJA5upE9jdoouTilkpJqFaPwbkNMka7Ng7RPm7S3qXCY0qh0ZnFWvNjTDEByYJXtw8IyGvE2OI2TRh6TRTi/cQ/eUmCp4YbBOU3YWpllyYMXXYEEgM8yBjcwYNTA0IKmzC/uU8TRl1mGJKDPclEuII7R+dQapuxtHNEE0Z1qhJpSi+yj3AFyzsmsLZ3TMaoSAlGVriMzJz/9YqhfnclJQmKb2MA8AKgcghshFV5qgAAAABJRU5ErkJggg==');
}
.check-color .mantine-Checkbox-input:checked {
  background-color: #003f6e;
  border-color: #003f6e;
}
.check-color .mantine-Checkbox-input {
  background: transparent;
  border-color: transparent;
}
.gm-style-iw-ch {
  display: none;
}
.gm-style-iw-chr {
  position: absolute;
  right: 10px;
  top: 15px;
}
.gm-style .gm-style-iw-c {
  border-radius: 0px;
  border: 1px solid #297f9d;
}
.gm-ui-hover-effect {
  width: auto !important;
  height: auto !important;
}
.gm-ui-hover-effect span {
  width: 15px !important;
  height: 15px !important;
  margin: 0px !important;
}
.infoWindow {
  padding: 10px 30px;
  padding-left: 0px;
}
.tee .custom-download a:hover svg path {
  stroke: #297f9d;
}
.show-badge + .show-plan {
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

@media (min-width: 1500px) {
  .myaccount-custom-viweplans {
    display: flex;
    gap: 80px;
  }
  .myaccount-custom-viweplans #rightside-container {
    margin-top: 274px;
  }
}
.myaccount-custom-viweplans #rightside-container {
  align-items: center;
}
.myaccount-custom-viweplans {
  background: #eeeef1;
}
.planModal .ChoosePlanModal {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.tee .mantine-Select-input {
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  -ms-appearance: none;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -khtml-border-radius: 4px;
}
.mantine-Select-input {
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  -ms-appearance: none;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  border-radius: 8px;
  -khtml-border-radius: 8px;
}
@supports (-webkit-touch-callout: none) {
  /* ios device specific */
  .tee .mantine-Select-input {
    border-radius: 4px;
  }
  .mantine-Select-input {
    border-radius: 8px;
  }
}
