import { faBolt, faInfoCircle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Text,
  Field,
  withDatasourceCheck,
  ImageField,
  Link,
  LinkField,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useAppSelector } from 'src/stores/store';
import { useState } from 'react';
import router from 'next/router';
import { DecodeURL, FormatPhoneNumber, FormattedDate } from 'src/utils/util';
import { QueryParamsMapType } from 'src/utils/query-params-mapping';
// import ExternalLink from 'components/Elements/ExternalLink/ExternalLink';

type MyPlanSelectionCardProps = ComponentProps & {
  fields: {
    Title: Field<string>;
    TermText: Field<string>;
    RateTypeText: Field<string>;
    DigitalRateText: Field<string>;
    RateText: Field<string>;
    CancellationFeeText: Field<string>;
    DocumentsText: Field<string>;
    NoPlanSelectionText: Field<string>;
    SolarEnergyTitle: Field<string>;
    PoweredByText: Field<string>;
    SunRunLogo: ImageField;
    EFLText: Field<string>;
    TOSText: Field<string>;
    YRACText: Field<string>;
    kwhText: Field<string>;
    SkipSolarPlansText: Field<string>;
    SkipSolarPlansLink: LinkField;
    ShowDetailsText: Field<string>;
    HideDetailsText: Field<string>;
    FixedMonthTermText: Field<string>;
    MonthToMonthTermText: Field<string>;
    FullNameText: Field<string>;
    EmailText: Field<string>;
    MobilePhoneText: Field<string>;
    DOBText: Field<string>;
    LanguageText: Field<string>;
    ServiceAddressText: Field<string>;
    BillingAddressText: Field<string>;
    ESIIDText: Field<string>;
    ServiceTypeText: Field<string>;
    SwitchLabel: Field<string>;
    MoveInLabel: Field<string>;
    RenewableEnergyText: Field<string>;
    EnrollmentDateText: Field<string>;
    ContractStartDateText: Field<string>;
    ContractEndDateText: Field<string>;
    PlanDetailText: Field<string>;
    BaseChargeLabel: Field<string>;
    CreditForkWhLabel: Field<string>;
    EnergyChargeLabel: Field<string>;
    AllkWHLabel: Field<string>;
    CurrentTDULabel: Field<string>;
    CurrentTDULink: Field<string>;
    YesLabel: Field<string>;
    NoLabel: Field<string>;
    PrimaryPhoneText: Field<string>;
    CancellationTermText: Field<string>;
    SameBillingAddress: Field<string>;
  };
  displayWithContactDetails?: boolean;
};

const MyPlanSelectionCard = (props: MyPlanSelectionCardProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  const { cint } = router.query as QueryParamsMapType;
  const [planDetailShow, setPlanDetailsShow] = useState<boolean>(false);
  let selectedPlan = undefined;
  let enrollment = undefined;
  let renewableEnergy = undefined;
  if (!isPageEditing) {
    selectedPlan = useAppSelector((state) => state.plans?.selectedPlan);
    enrollment = useAppSelector((state) => state.enrollment);
    renewableEnergy = useAppSelector((state) => state?.enrollment?.isRenewableEnergy);
  }
  const customerInfo = enrollment?.customerInfo;
  const serviceInfo = enrollment?.serviceInfo;
  const billingInfo = enrollment?.billingInfo;
  const today = new Date();
  const formattedDate = FormattedDate(today.toLocaleString());

  if (selectedPlan?.planName != '') {
    return (
      <div className="w-full mb-4 rounded-lg py-2">
        <Text
          className="text-[12px] font-primaryBold text-textQuattuordenary pl-2"
          tag="p"
          field={props.fields.Title}
        />
        <div className="bg-bgOctodenary border-b-2 flex flex-row items-center justify-center py-2 border-borderOctodenary rounded-t-lg">
          <FontAwesomeIcon size={'sm'} icon={faBolt} />
          <Text
            className="text-[14px] font-primaryBold text-center text-textQuattuordenary pl-2"
            tag="div"
            field={{ value: selectedPlan?.planName }}
          />
        </div>
        <div className="px-3 pb-3 bg-bgOctodenary rounded-b-lg">
          <div className="border-t-[1px] border-borderQuattuordenary">
            <table className="font-primaryRegular text-minus3 w-full mt-2">
              <tbody>
                <tr className="">
                  <td className="text-[12px] w-[40%] text-textQuattuordenary">
                    {props.fields?.TermText?.value}
                  </td>
                  <td className="pl-2 text-[12px] w-[60%] font-primaryBold text-textQuattuordenary">
                    {selectedPlan?.term > 0
                      ? props.fields?.FixedMonthTermText?.value.replace(
                          '${term}',
                          selectedPlan?.term.toString()
                        )
                      : props.fields?.MonthToMonthTermText?.value}
                  </td>
                </tr>
                <tr className="">
                  <td className="text-[12px] w-[40%] text-textQuattuordenary">
                    {props.fields?.RateTypeText?.value}
                  </td>
                  <td className="pl-2 text-[12px] w-[60%] font-primaryBold text-textQuattuordenary">
                    {selectedPlan?.rateType}
                  </td>
                </tr>
                <tr className="">
                  <td className="text-[12px] w-[40%] text-textQuattuordenary">
                    {props.fields?.RateText?.value}
                  </td>
                  <td className="pl-2 text-[12px] w-[60%] font-primaryBold text-textQuattuordenary">
                    {(selectedPlan?.rate * 100).toFixed(1)}¢ {props.fields?.kwhText?.value}
                  </td>
                </tr>

                <tr className="">
                  <td className="text-[12px] w-[40%] text-textQuattuordenary">
                    {props.fields?.CancellationFeeText?.value}
                  </td>
                  <td className="pl-2 text-[12px] w-[60%] font-primaryBold text-textQuattuordenary">
                    {selectedPlan?.cancellationFee} {props?.fields?.CancellationTermText?.value}
                  </td>
                </tr>
                <tr className="">
                  <td className="text-[12px] w-[40%] text-textQuattuordenary">
                    {props.fields?.DocumentsText?.value}
                  </td>
                  <td className="pl-2 text-[12px] w-[60%] font-primaryBold text-textQuattuordenary">
                    <div className="flex flex-row">
                      <div className="flex items-center">
                        <Link
                          target="_blank"
                          field={{ value: { href: DecodeURL(selectedPlan?.EFLUrl ?? '') } }}
                          className="underline text-xs items-center cursor-pointer  text-textPrimary hover:text-textSecondary"
                        >
                          {props.fields?.EFLText?.value}
                        </Link>
                      </div>
                      <div className="flex items-center px-2">
                        <Link
                          target="_blank"
                          field={{ value: { href: DecodeURL(selectedPlan?.TOSUrl ?? '') } }}
                          className="underline text-xs items-center cursor-pointer text-textPrimary hover:text-textSecondary"
                        >
                          {props.fields?.TOSText?.value}
                        </Link>
                      </div>
                      <div className="flex items-center">
                        <Link
                          target="_blank"
                          field={{ value: { href: DecodeURL(selectedPlan?.YRCUrl ?? '') } }}
                          className="underline text-xs items-center cursor-pointer text-textPrimary hover:text-textSecondary"
                        >
                          {props.fields?.YRACText?.value}
                        </Link>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div className="pt-4">
          {props?.displayWithContactDetails && (
            <div className=" bg-bgNovemdenary rounded-lg py-2">
              <div className="mx-3 py-2 flex flex-row items-center justify-center">
                <table className="font-primaryRegular text-minus3 w-full">
                  <tbody>
                    <tr className="">
                      <td className="text-[12px] w-[40%] text-textQuattuordenary">
                        {props.fields?.FullNameText?.value}
                      </td>
                      <td className="pl-2 text-[12px] w-[60%] text-textQuattuordenary">
                        {customerInfo?.firstName}
                        {''} {customerInfo?.lastName}
                      </td>
                    </tr>
                    <tr className="">
                      <td className="text-[12px] w-[40%] text-textQuattuordenary">
                        {props.fields?.EmailText?.value}
                      </td>
                      <td className="pl-2 text-[12px] w-[60%] text-textQuattuordenary">
                        {customerInfo?.email}
                      </td>
                    </tr>

                    <tr className="">
                      <td className="text-[12px] w-[40%] text-textQuattuordenary">
                        {props.fields?.PrimaryPhoneText?.value}
                      </td>
                      <td className="pl-2 text-[12px] w-[60%] text-textQuattuordenary">
                        {customerInfo?.phoneNumber
                          ? FormatPhoneNumber(customerInfo?.phoneNumber)
                          : 'N/A'}
                      </td>
                    </tr>
                    <tr className="">
                      <td className="text-[12px] w-[40%] text-textQuattuordenary">
                        {props.fields?.MobilePhoneText?.value}
                      </td>
                      <td className="pl-2 text-[12px] w-[60%] text-textQuattuordenary">
                        {enrollment?.customerInfo?.isMobile
                          ? props.fields.YesLabel.value
                          : props.fields.NoLabel.value}
                      </td>
                    </tr>
                    <tr className="">
                      <td className="text-[12px] w-[40%] text-textQuattuordenary">
                        {props.fields?.DOBText?.value}
                      </td>
                      <td className="pl-2 text-[12px] w-[60%] text-textQuattuordenary">
                        {customerInfo?.dateOfBirth
                          ? FormattedDate(
                              customerInfo?.dateOfBirth ? customerInfo?.dateOfBirth : ''
                            )
                          : 'N/A'}
                      </td>
                    </tr>
                    <tr className="">
                      <td className="text-[12px] w-[40%] text-textQuattuordenary">
                        {props.fields?.LanguageText?.value}
                      </td>
                      <td className="pl-2 text-[12px] w-[60%] text-textQuattuordenary">
                        {enrollment?.languageOptionalValue}
                      </td>
                    </tr>
                    <tr className="">
                      <td className="text-[12px] w-[40%] text-textQuattuordenary">
                        {props.fields?.ServiceAddressText?.value}
                      </td>
                      <td className="pl-2 text-[12px] w-[60%] text-textQuattuordenary">
                        {`${serviceInfo?.houseNbr ?? ''} ${serviceInfo?.street ?? ''} ${
                          serviceInfo?.unit ?? ''
                        } ${serviceInfo?.city ?? ''}, ${serviceInfo?.state ?? ''} ${
                          serviceInfo?.postalCode ?? ''
                        }`.trim()}
                      </td>
                    </tr>
                    <tr className="">
                      <td className="text-[12px] w-[40%] text-textQuattuordenary">
                        {props.fields?.BillingAddressText?.value}
                      </td>
                      <td className="pl-2 text-[12px] w-[60%] text-textQuattuordenary">
                        {billingInfo?.isSameAddress
                          ? props.fields.SameBillingAddress.value
                          : serviceInfo?.poBoxZipCode !== ''
                          ? `${serviceInfo?.poBox ?? ''} ${serviceInfo?.poBoxCity ?? ''} ,${
                              serviceInfo?.poBoxState ?? ''
                            } ${serviceInfo?.poBoxZipCode ?? ''}`.trim()
                          : `${billingInfo?.billingStreetNumber ?? ''} ${
                              billingInfo?.billingStreetAddress ?? ''
                            } ${billingInfo?.billingAptOrUnit ?? ''} ${
                              billingInfo?.billingCity ?? ''
                            }, ${billingInfo?.billingState ?? ''} ${
                              billingInfo?.billingZipCode ?? ''
                            }`.trim()}
                      </td>
                    </tr>
                    <tr className="">
                      <td className="text-[12px] w-[40%] text-textQuattuordenary">
                        {props.fields?.ESIIDText?.value}
                      </td>
                      <td className="pl-2 text-[12px] w-[60%] text-textQuattuordenary">
                        {serviceInfo?.esiid}
                      </td>
                    </tr>
                    <tr className="">
                      <td className="text-[12px] w-[40%] text-textQuattuordenary">
                        {props.fields?.ServiceTypeText?.value}
                      </td>
                      <td className="pl-2 text-[12px] w-[60%] text-textQuattuordenary">
                        {cint === '4'
                          ? props.fields?.MoveInLabel?.value
                          : props.fields?.SwitchLabel?.value}
                      </td>
                    </tr>

                    <tr className="">
                      <td className="text-[12px] w-[40%] text-textQuattuordenary">
                        {props.fields?.RenewableEnergyText?.value}
                      </td>
                      <td className="pl-2 text-[12px] w-[60%] text-textQuattuordenary">
                        {renewableEnergy === true
                          ? props?.fields?.YesLabel?.value
                          : props?.fields?.NoLabel?.value}
                      </td>
                    </tr>
                    <tr className="">
                      <td className="text-[12px] w-[40%] text-textQuattuordenary">
                        {props.fields?.EnrollmentDateText?.value}
                      </td>
                      <td className="pl-2 text-[12px] w-[60%] text-textQuattuordenary">
                        {formattedDate}
                      </td>
                    </tr>
                    <tr className="">
                      <td className="text-[12px] w-[40%] text-textQuattuordenary">
                        {props.fields?.ContractStartDateText?.value}
                      </td>
                      <td className="pl-2 text-[12px] w-[60%] text-textQuattuordenary">
                        {serviceInfo?.startDate}
                      </td>
                    </tr>
                    <tr className="">
                      <td
                        onClick={() => setPlanDetailsShow(!planDetailShow)}
                        className="text-[12px] w-[40%] underline cursor-pointer text-textQuattuordenary align-top"
                      >
                        {props.fields?.PlanDetailText?.value}
                        <FontAwesomeIcon
                          size={'sm'}
                          icon={faInfoCircle}
                          style={{ paddingLeft: 2 }}
                        />
                      </td>
                      {planDetailShow && (
                        <td className="pl-2 text-textQuattuordenary text-[12px] w-[60%]">
                          {props.fields?.BaseChargeLabel?.value} {selectedPlan?.baseCharge}
                          <br></br>
                          {props.fields?.CreditForkWhLabel?.value}
                          {'N/A'}
                          <br></br>
                          {props.fields?.EnergyChargeLabel?.value}
                          {selectedPlan?.energyCharge}
                          <br></br>
                          {props.fields?.AllkWHLabel?.value}
                          {
                            selectedPlan?.trieProductRateDetails?.find(
                              (item) => item.range.toLowerCase() === 'all kwh'
                            )?.value
                          }
                          <br></br>
                          {props.fields?.CurrentTDULabel?.value}
                          <br></br>
                          {/* <ExternalLink
                          title={props.fields?.CurrentTDULink?.value?.href}
                          href={props.fields?.CurrentTDULink?.value?.href as string}
                          className="underline cursor-pointer text-textPrimary hover:text-textSecondary"
                          target="_blank"
                        >
                          {props.fields?.CurrentTDULink?.value?.url}
                        </ExternalLink> */}
                          <Link
                            target="_blank"
                            field={{
                              value: {
                                href: DecodeURL(
                                  props.fields?.CurrentTDULink?.value?.href as string
                                ),
                              },
                            }}
                            className="underline items-center cursor-pointer text-textPrimary hover:text-textSecondary"
                          >
                            {props.fields?.CurrentTDULink?.value?.url}
                          </Link>
                        </td>
                      )}
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return <></>;
};

export { MyPlanSelectionCard };
const Component = withDatasourceCheck()<MyPlanSelectionCardProps>(MyPlanSelectionCard);
export default aiLogger(Component, Component.name);
