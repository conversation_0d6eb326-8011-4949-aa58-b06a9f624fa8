import {
  faCircleUser,
  faChevronUp,
  faChevronDown,
  faChevronsRight,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Popover, UnstyledButton } from '@mantine/core';
import {
  Text,
  Field,
  withDatasourceCheck,
  Link,
  LinkField,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { getCookie } from 'cookies-next';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useAppSelector } from 'src/stores/store';
import { getSwapOrRenewalStatus } from 'src/utils/getSwap';
import { removeURLParams } from 'src/utils/util';
import LanguageSelectorMobile from 'components/common/LanguageSelectorMobile/LanguageSelectorMobile';

type MobileMenuProps = ComponentProps & {
  fields: {
    Menus: MenuList[];
    WelcomeText: Field<string>;
    MyAccountText: Field<string>;
  };
};

interface MenuList {
  displayName: Field<string>;
  fields: {
    Icon: Field<string>;
    NavText: Field<string>;
    NavLink: LinkField;
    IsHidden: Field<boolean>;
    CssClass: Field<string>;
    HighlightURLs: Field<string>;
    Submenu: MenuList[];
    HideForBusinessUser: Field<boolean>;
  };
  id: Field<string>;
  name: Field<string>;
  url: Field<string>;
}
function MenuListComponent(props: {
  menu: MenuList;
  key: number;
  setShowMenu: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  const [isToggled, setIsToggled] = useState(false);
  let isSubToggled = false;
  const router = useRouter();
  const currentPath = removeURLParams(router.asPath);
  const swapOrRenewal = useAppSelector((state) => state.authuser.renewal);
  const isBusinessUser = useAppSelector((state) => state.authuser.isBusinessUser);

  const impersonatedCookieValue = getCookie('isImpersonatedUser');
  const impersonatedUser = impersonatedCookieValue === 'true';

  const Submenus = props.menu.fields.Submenu.map((submenu, submenuindex) => {
    let isSubNavHighlight = false;
    const highlightURLs: string[] = submenu.fields.HighlightURLs.value
      .split(',')
      .map((val) => val.trim());
    highlightURLs.forEach((val) => {
      if (val && currentPath.startsWith(val)) {
        isSubNavHighlight = true;
      }
      if (currentPath === submenu.fields.NavLink.value.href) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        isSubNavHighlight = true;
        isSubToggled = true;
      }
    });

    const submenuresponse = getSwapOrRenewalStatus(
      swapOrRenewal?.swapOrRenewalStatus,
      swapOrRenewal.contractAccount,
      swapOrRenewal.esiid,
      swapOrRenewal.promo,
      submenu,
      impersonatedUser
    );

    if (submenuresponse) submenu.fields.NavLink.value.href = submenuresponse.navLink;

    if (props.menu.fields.HideForBusinessUser?.value === true && isBusinessUser) return null;
    else
      return (
        <li
          key={submenuindex}
          className={`${submenuresponse.allowDisplay ? 'block' : 'hidden'} ${submenu.fields.CssClass.value
            }`}
        >
          <Link
            field={submenu.fields.NavLink}
            className={`${isSubNavHighlight
                ? 'text-textPrimary hover:text-textSecondary font-primarybold'
                : 'text-textQuattuordenary'
              } font-primaryRegular text-minus1 py-1 block`}
          >
            {submenu.fields.NavText.value}
          </Link>
        </li>
      );
  });

  const highlightURLs: string[] = props.menu.fields.HighlightURLs.value
    .split(',')
    .map((val) => val.trim());

  let isNavHighlight = false;
  highlightURLs.forEach((val) => {
    if (val && currentPath.includes(val)) {
      isNavHighlight = true;
    }
  });
  const menuresponse = getSwapOrRenewalStatus(
    swapOrRenewal?.swapOrRenewalStatus,
    swapOrRenewal.contractAccount,
    swapOrRenewal.esiid,
    swapOrRenewal.promo,
    props.menu,
    impersonatedUser
  );
  if (menuresponse) props.menu.fields.NavLink.value.href = menuresponse.navLink;
  if (currentPath === props.menu.fields.NavLink.value.href && !isToggled && !isSubToggled) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    isNavHighlight = true;
  }

  useEffect(() => {
    if (!isNavHighlight && Submenus.length > 0 && !isSubToggled) {
      setIsToggled(false);
    }
    if (isSubToggled) {
      setIsToggled(true);
    }
  }, [isNavHighlight, isSubToggled]);

  const handleClick = () => {
    isNavHighlight = false;
    setIsToggled((value) => !value);
    props.setShowMenu(true);
  };

  if (props.menu.fields.HideForBusinessUser?.value === true && isBusinessUser) return null;
  else
    return (
      <li
        key={props.key}
        className={`${menuresponse.allowDisplay ? 'block' : 'hidden'} ${props.menu.fields.CssClass.value
          }py-3 border-t first:border-t-0 border-solid border-borderPrimary`}
      >
        <div className="flex flex-row items-center">
          <FontAwesomeIcon
            className={`${isNavHighlight || isSubToggled ? 'hidden' : 'hidden'
              } text-textPrimary hover:text-textSecondary text-minus2`}
            icon={faChevronsRight}
          />
          {Submenus.length == 0 && (
            <Link
              aria-current={isNavHighlight ? 'page' : undefined}
              className={`${isNavHighlight
                  ? 'text-textPrimary border-solid border-cactus'
                  : 'text-textQuattuordenary border-transparent'
                } leading-[24px] font-primaryRegular cursor-pointer flex gap-2 pl-2 border-l-2 border-solid text-textQuattuordenary hover:text-textSecondary text-minus1 w-full border-0`}
              field={{ value: props.menu.fields.NavLink.value }}
              role="menuitem"
            >
              <span className="my-account-showMenu relative">
                {props.menu.fields.NavText.value}
              </span>
            </Link>
          )}
          {Submenus.length > 0 && (
            <Link
              aria-current={isNavHighlight ? 'page' : undefined}
              className={`${isNavHighlight || isSubToggled
                  ? 'text-textPrimary border-solid border-cactus'
                  : 'text-textQuattuordenary border-transparent font-primaryRegular ml-0'
                } leading-[24px] font-primarybold cursor-pointer flex gap-2 pl-2 border-l-2 border-solid text-textQuattuordenary hover:text-textSecondary text-minus2 tee:text-minus2 tee:w-full border-0`}
              field={{ value: props.menu.fields.NavLink.value }}
              onClick={(e) => {
                e.preventDefault();
                handleClick();
              }}
              role="menuitem"
            >
              <span className="my-account-showMenu relative">
                {props.menu.fields.NavText.value}
              </span>
            </Link>
          )}
          {Submenus.length > 0 && (
            <FontAwesomeIcon
              className="ml-auto mr-1 cursor-pointer text-textPrimary hover:text-textSecondary"
              icon={isToggled ? faChevronUp : faChevronDown}
              onClick={() => handleClick()}
            />
          )}
        </div>
        {isToggled && (
          <div className="flex flex-row gap-3 py-3 ml-0">
            <div className="border-l-2 border-textOctonary hidden"></div>
            <ul className="flex flex-col gap-3 ml-3 w-full">{Submenus}</ul>
          </div>
        )}
      </li>
    );
}

const MobileMenu = (props: MobileMenuProps): JSX.Element | null => {
  // const [activeLink, setActiveLink] = useState('');
  const [showMenu, setShowMenu] = useState(false);
  const menuList: MenuList[] = props.fields.Menus;
  const custFName = getCookie('customer_name');
  const [firstName] = useState(custFName !== undefined ? custFName : '');
  const router = useRouter();

  //TODO: Change to props in the child component
  useEffect(() => {
    setShowMenu(false);
  }, [router]);

  const MenuLinks = menuList.map((menu, index) => (
    <MenuListComponent menu={menu} key={index} setShowMenu={setShowMenu} />
  ));

  return (
    <div className="ml-auto">
      {/* mobile menu */}
      <div className="mobile">
        <Popover opened={showMenu} width={260} position="bottom-start" trapFocus>
          <Popover.Target>
            <UnstyledButton
              className="text-textPrimary text-base font-primaryBlack flex flex-row items-center"
              onClick={() => setShowMenu(!showMenu)}
            >
              {typeof firstName === 'string' && firstName.length > 0 && (
                <div className="font-primaryBold rounded-full flex items-center justify-center h-auto w-[32px] p-[3px] text-[16px] bg-bgPrimary text-textQuinary py-[3px]">
                  <FontAwesomeIcon className="text-textPrimary hidden" icon={faCircleUser} />
                  <span>
                    {typeof firstName === 'string' && firstName.length > 0 ? firstName[0] : <></>}
                  </span>
                </div>
              )}
              <div className="pl-[15px] flex flex-row gap-2">
                {firstName === '' ? (
                  <div>Loading...</div>
                ) : (
                  <Text
                    tag="p"
                    className="text-charcoal-full font-primaryRegular text-minus2 leading-[24px] tracking-[-0.25px] uppercase"
                    field={{ value: props.fields.WelcomeText.value + ' ' + firstName }}
                  />
                )}
                <div className="flex items-center">
                  <Text
                    tag="p"
                    className="text-textPrimary font-primaryRegular text-base leading-[22px]"
                    field={{ value: props.fields.MyAccountText.value }}
                  />
                  {showMenu ? (
                    <FontAwesomeIcon
                      icon={faChevronUp}
                      className=" pl-[4px] text-textPrimary hover:text-textSecondary h-[15px] w-[15px] border-solid"
                    />
                  ) : (
                    <FontAwesomeIcon
                      icon={faChevronDown}
                      className=" pl-[4px] text-textPrimary hover:text-textSecondary h-[15px] w-[15px] border-solid"
                    />
                  )}
                </div>
              </div>
            </UnstyledButton>
          </Popover.Target>
          <Popover.Dropdown className="px-4 py-6 block min-w-[228px] rounded-none h-[100vh] sm:h-full top-[0!important] mt-[52px] left-0 w-[100%!important]  shadow-none border-0">
            {/* menu */}
            <nav aria-label="Main navigation">
              <ul role="menu" className="grid gap-2">
                {MenuLinks}
              </ul>
              <div>
                <LanguageSelectorMobile />
              </div>
            </nav>
          </Popover.Dropdown>
        </Popover>
      </div>
    </div>
  );
};
export { MobileMenu };
const Component = withDatasourceCheck()<MobileMenuProps>(MobileMenu);
export default aiLogger(Component, Component.name);
