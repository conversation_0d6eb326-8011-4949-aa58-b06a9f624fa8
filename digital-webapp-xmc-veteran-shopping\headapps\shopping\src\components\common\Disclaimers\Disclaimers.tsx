import { RichText, Field, withDatasourceCheck } from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';

type DisclaimersProps = ComponentProps & {
  fields: {
    DisclaimerText: Field<string>;
  };
};

const Disclaimers = (props: DisclaimersProps): JSX.Element => (
  <div className="disclaimer-section max-w-1008 pb-6 sm:pb-8 w-full px-2 sm:px-0 sm:ml-[460px]">
    <div className="w-full max-w-[830px] sm:max-w-[790px] ml-0  h-0 border-b-2 border-borderVigintiternary mt-5 sm:mt-[30px] sm:w-full wide:w-full wide:ml-0 ipad:w-full ipad:ml-0" />
    <RichText
      className="max-w-[830] sm:max-w-[790px] w-full pt-5 m-0 ml-0 text-textQuattuordenary text-minus3 sm:text-minus2 p-[20px] sm:p-[30px] pr-[15px] sm:pl-0 sm:pr-0 ipad:ml-0 ipad:pl-[15px] ipad:pr-[15px]"
      field={props.fields.DisclaimerText}
    />
  </div>
);

export { Disclaimers };
const Component = withDatasourceCheck()<DisclaimersProps>(Disclaimers);
export default aiLogger(Component, Component.name);
