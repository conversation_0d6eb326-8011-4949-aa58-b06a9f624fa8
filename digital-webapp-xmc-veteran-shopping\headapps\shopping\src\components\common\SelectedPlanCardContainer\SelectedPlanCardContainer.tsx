import {
  Field,
  Text,
  useSitecoreContext,
  withDatasourceCheck,
} from '@sitecore-jss/sitecore-jss-nextjs';
import SelectedPlanCard from 'components/common/SelectedPlanCard/SelectedPlanCard';
import { ComponentProps } from 'lib/component-props';
import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronUp, faChevronDown } from '@fortawesome/pro-light-svg-icons';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useAppSelector } from 'src/stores/store';

export interface SelectedPlanCardContainerProps extends ComponentProps {
  fields: {
    SelectedPlanText: Field<string>;
    RateText: Field<string>;
    TermText: Field<string>;
    SeeDetailsText: Field<string>;
    PlanDetailsandInformationText: Field<string>;
    EarlyCancellationFeeText: Field<string>;
    EarlyCancellationFeeHelpText: Field<string>;
    EarlyCancellationFeeTooltip: Field<string>;
    ElectricityFactsLabelText: Field<string>;
    TermsOfServiceText: Field<string>;
    YourRightsAsACustomerText: Field<string>;
    HideDetailsText: Field<string>;
    Description: Field<string>;
    ProductDetailsandInformationText: Field<string>;
  };
}

const SelectedPlanCardContainer = (props: SelectedPlanCardContainerProps): JSX.Element => {
  const [showPlans, setShowPlans] = useState(false);
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let selectedPlan = undefined;
  let addonPlans = undefined;
  if (!isPageEditing) {
    selectedPlan = useAppSelector((state) => state?.plans?.selectedPlan);
    addonPlans = useAppSelector((state) => state?.plans?.addonPlans);
  }
  // const AdditionalProducts

  return (
    <div className="selected-plans-view w-full  sm:min-h-[48px] min-h-[48px] overflow-visible bg-white flex flex-col sm:flex-row justify-start items-center gap-5 px-0 border-b-2 border-borderVigintiternary ml-0 wide:ml-0 ipad:ml-0 ipad:pl-6 wide:pl-6 sm:ml-[-90px]">
      <div className="max-w-[1200px] w-full">
        <div className="flex flex-col sm:flex-row items-center sm:items-center gap-5 justify-start px-6 sm:px-6 sm:pl-[110px] pb-2">
          <div
            className={`flex flex-row gap-3 items-center ${showPlans ? 'mt-[12px] sm:mt-0' : 'mt-[12px]'
              }`}
          >
            <Text
              tag="p"
              className="text-[14px] text-minus1 leading-[16px] text-textQuattuordenary font-primaryRegular sm:pt-[12px] w-[120px]"
              field={props.fields.SelectedPlanText}
            />
            <div onClick={() => setShowPlans(!showPlans)} className="cursor-pointer sm:hidden">
              {showPlans ? (
                <FontAwesomeIcon
                  icon={faChevronUp}
                  className="text-textPrimary relative top-[3px] "
                />
              ) : (
                <FontAwesomeIcon icon={faChevronDown} className="text-textPrimary" />
              )}
            </div>
          </div>
          <div
            className={`${showPlans ? 'block' : 'hidden'
              }  sm:h-[48px] w-full sm:flex  sm:overflow-y-visible sm:pl-[35px]`}
          >
            <SelectedPlanCard fields={props} variant="primary" {...selectedPlan} />
          </div>
          {addonPlans && Object.keys(addonPlans).length !== 0 && (
            <div className={`flex-row gap-3 ${showPlans ? 'flex' : 'hidden'} sm:flex`}>
              <Text
                tag="p"
                className="text-[14px] leading-[16px] text-textQuattuordenary font-primaryRegular sm:mt-4"
                field={{ value: 'Additional Products:' }}
              />
            </div>
          )}
          <div className={`${showPlans ? 'flex flex-col' : 'hidden'} sm:flex sm:flex-col sm:gap-3`}>
            {addonPlans &&
              Object.keys(addonPlans).map((addonPlanKey) => {
                return (
                  <div key={addonPlanKey} className="sm:overflow-y-visible">
                    <SelectedPlanCard
                      variant="secondary"
                      fields={props}
                      {...addonPlans[addonPlanKey]}
                    />
                  </div>
                );
              })}
          </div>
        </div>
      </div>
    </div>
  );
};

export { SelectedPlanCardContainer };
const Component = withDatasourceCheck()<SelectedPlanCardContainerProps>(SelectedPlanCardContainer);
export default aiLogger(Component, Component.name);
