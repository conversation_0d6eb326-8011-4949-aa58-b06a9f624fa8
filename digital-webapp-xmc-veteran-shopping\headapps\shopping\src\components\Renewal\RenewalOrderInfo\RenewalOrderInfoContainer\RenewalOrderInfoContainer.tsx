import { Checkbox } from '@mantine/core';
import { useForm, zodResolver } from '@mantine/form';
import {
  Field,
  withDatasource<PERSON>heck,
  LinkField,
  Placeholder,
  RichText,
  GetServerSideComponentProps,
  useComponentProps,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import axios, { AxiosError } from 'axios-1.4';
import Button from 'components/Elements/Button/Button';
import { EVDisclaimer } from 'components/MiniConfirmation/EVSelection/EVSelection';
import { logErrorToAppInsights } from 'lib/app-insights-log-error';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useLoader } from 'src/hooks/modalhooks';
import MyAccountAPI from 'src/services/MyAccountAPI';
import { GetMeterReadDates } from 'src/services/MyAccountAPI/types';
import { setEV } from 'src/stores/planSlice';
import { setRenewalInformation, setRenewalServiceStartDate } from 'src/stores/renewalSlice';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import { serverGetPartnerCookie } from 'src/utils/authTokenUtil';
import { DwellingType, QueryParamsMapType } from 'src/utils/query-params-mapping';
import { z } from 'zod';

type RenewalOrderInfoContainerProps = ComponentProps & {
  fields: {
    ButtonText: Field<string>;
    RedirectionLink: LinkField;
    RenewMyEnergyPlan: Field<string>;
    RenewMyEnergyPlanError: Field<string>;
    TermsAndConditions: Field<string>;
    TermsAndConditonsError: Field<string>;
    EVDisclaimer: EVDisclaimer;
    PendingTransactionRedirectionLink: LinkField;
  };
};

export interface NewPlanInfoType {
  BusinessPartnerNumber: string;
  ContractAccountNumber: string;
  StartDate: string;
  Esiid: string;
  ExtId: string | null;
  DwellingType: string;
  ProductId: string;
  ProductTerm: string;
  CampaignId: string;
}

export interface NewPlanInfoFormType {
  StartDate: string;
  DefaultStartDate: string;
  acceptAuthorization: boolean;
  termsOfService: boolean;
}

interface RenewalOrderInformationData {
  meterReadDates: GetMeterReadDates | null;
}

let dispatch: ReturnType<typeof useAppDispatch>;
const RenewalOrderInfoContainer = (props: RenewalOrderInfoContainerProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let productTerm = undefined;
  let productRate = undefined;
  let authUser = undefined;
  let ev = undefined;
  let selectedPlan = undefined;
  let tosUrl = undefined;
  if (!isPageEditing) {
    dispatch = useAppDispatch();
    productTerm = useAppSelector((state) => state.plans.selectedPlan.term);
    productRate = useAppSelector((state) => state.plans.selectedPlan.rate);
    authUser = useAppSelector((state) => state.authuser);
    ev = useAppSelector((state) => state.plans.selectedPlan.ev);
    selectedPlan = useAppSelector((state) => state.plans.selectedPlan);
    tosUrl = useAppSelector((state) => state.plans.selectedPlan.TOSUrl);
  }
  const router = useRouter();
  const { openModal } = useLoader();
  const { esiid, contractaccount, dwel, planid } = router.query as QueryParamsMapType;
  const data = useComponentProps<RenewalOrderInformationData>(props.rendering.uid);
  const [isEVDisclaimerSelected, setIsEVDisclaimerSelected] = useState(false);
  const isEV = ev && props.fields.EVDisclaimer.fields.EVModels.value.includes(ev);
  console.log('Meter Read Dates', data);

  useEffect(() => {
    if (!isPageEditing) {
      dispatch(
        setEV({
          isEVSelected: isEVDisclaimerSelected,
          EVDisclaimerMessage: props.fields.EVDisclaimer.fields.EVDisclaimerMessage.value,
          DiclaimerErrorText: '',
          EVModels: props.fields.EVDisclaimer.fields.EVModels.value,
        })
      );
    }
  }, [
    dispatch,
    isEVDisclaimerSelected,
    props.fields.EVDisclaimer.fields.EVDisclaimerMessage.value,
    props.fields.EVDisclaimer.fields.EVModels.value,
  ]);

  const PlaceOrderSchema = z.object({
    acceptAuthorization: z.literal(true, {
      errorMap: () => ({
        message: props.fields.RenewMyEnergyPlanError.value,
      }),
    }),
    termsOfService: z.literal(true, {
      errorMap: () => ({
        message: props.fields.TermsAndConditonsError.value,
      }),
    }),
  });

  const PendingTransactionStatus = async () => {
    const response = await axios.get('/api/myaccount/transfer/pendingtransactionstatus', {
      params: {
        esiid: esiid,
      },
    });
    return response?.data?.result;
  };

  const form = useForm<NewPlanInfoFormType>({
    initialValues: {
      StartDate:
        typeof data?.meterReadDates?.result?.DefaultDate === 'string'
          ? data.meterReadDates?.result?.DefaultDate
          : '',
      DefaultStartDate: '',
      acceptAuthorization: false,
      termsOfService: false,
    },
    validate: zodResolver(PlaceOrderSchema),
    validateInputOnChange: true,
    validateInputOnBlur: true,
  });

  const submitRenewalOrder = async () => {
    const req = {
      BusinessPartnerNumber: authUser?.bpNumber,
      ContractAccountNumber: contractaccount,
      StartDate:
        form.values.StartDate === ''
          ? data?.meterReadDates?.result?.DefaultDate
          : form.values.StartDate,
      Esiid: esiid,
      ExtId: '',
      DwellingType: DwellingType[dwel as string],
      ProductId: planid,
      ProductTerm: productTerm?.toString(),
      selectedPlanRate: (productRate * 100).toFixed(1).toString(),
      CampaignId: selectedPlan?.campaignId,
    };

    form.validate();

    if (form.isValid()) {
      openModal();
      try {
        const pendingtransactionstatus = await PendingTransactionStatus();
        if (pendingtransactionstatus?.isPending) {
          router.push({
            pathname: props.fields.PendingTransactionRedirectionLink.value.href,
            query: { ...router.query },
          });
        } else {
          const response = await axios.post('/api/myaccount/renewal/createswap', req);
          if (response.data.result.indicator === 'Success') {
            if (!isPageEditing) {
              dispatch(setRenewalInformation(response.data.result.serviceContractNumber));
              dispatch(setRenewalServiceStartDate(req.StartDate !== undefined ? req.StartDate : ''));
            }
            router.push({
              pathname: props.fields.RedirectionLink.value.href,
              query: { ...router.query },
            });
          } else {
            router.push({
              pathname: '/oops',
              query: { ...router.query },
            });
          }
        }
      } catch (error: unknown) {
        const err = error as AxiosError;
        logErrorToAppInsights(err, {
          componentStack: 'RenewalOrderInfoContainer-createswap',
        });
        const queryParams = {
          errorCode: 'RenewalOrderInfoContainer-createswap',
        };
        router.push({
          pathname: '/oops',
          query: { ...queryParams },
        });
      }
    }
  };
  let TOSLink = props.fields.TermsAndConditions.value.replace('{planId}', planid);
  TOSLink = TOSLink.replace('{TermsOfServiceUrl}', tosUrl);
  TOSLink = TOSLink.replace('<a ', '<a class="underline cursor-pointer text-textPrimary" ');
  return (
    <div className="flex flex-col mb-16 px-6 sm:px-0 ml-0 sm:ml-[410px] wide:ml-0 ipad:ml-0 ipad:pl-6 wide:pl-6">
      <form>
        <Placeholder
          name="jss-renewal-orderinfo"
          rendering={props.rendering}
          form={form}
          render={(components) => {
            return (
              <div className="">
                {components.map((component, index) => {
                  return (
                    <div key={index} className={`flex flex-col w-full`}>
                      {component}
                    </div>
                  );
                })}
              </div>
            );
          }}
          defaultStartDate={data?.meterReadDates?.result?.DefaultDate}
          meterReadList={data?.meterReadDates?.result?.ListOfMeterRead}
        />
        <div className="flex flex-col w-full px-4 max-w-[832px] ml-[0px] sm:px-0 my-8 mb-5 ipad:px-[20px] ipad:ml-0 wide:ml-0 wide:px-[20px] border-t-2 border-borderVigintiternary"></div>
        <div className="flex flex-col w-full max-w-[832px]  sm:px-0 px-6 my-8 mb-0 ipad:px-[20px] wide:px-[20px] ">
          <div className="font-primaryRegular mb-[35px] ">
            <Checkbox
              label={
                <RichText
                  tag="p"
                  field={props.fields.RenewMyEnergyPlan}
                  className="font-primaryRegular text-textUndenary tracking-normal leading-[20px] text-[16px]"
                />
              }
              {...form.getInputProps('acceptAuthorization', { type: 'checkbox' })}
            />
          </div>
          <div className="font-primaryRegular mb-[35px]">
            <Checkbox
              className="font-primaryRegular text-textUndenary tracking-normal"
              label={
                <RichText
                  tag="p"
                  className="font-primaryRegular text-textUndenary tracking-normal leading-[20px] text-[16px]"
                  field={{
                    value: TOSLink,
                  }}
                />
              }
              {...form.getInputProps('termsOfService', { type: 'checkbox' })}
            />
          </div>
          {isEV && (
            <div className="font-primaryRegular mb-[35px]">
              <Checkbox
                checked={isEVDisclaimerSelected}
                onChange={() => setIsEVDisclaimerSelected(!isEVDisclaimerSelected)}
                label={
                  <RichText
                    tag="p"
                    className="font-primaryRegular text-textUndenary tracking-normal"
                    field={{ value: props.fields.EVDisclaimer.fields.EVCheckbox.value }}
                  />
                }
              />
            </div>
          )}
        </div>
        <div className="my-[10px] max-w-[832px] sm:px-0 px-6 mb-16 ipad:pl-[20px] wide:pl-[20px] m-auto sm:m-0  sm:mb-8">
          <Button
            type="button"
            className="w-auto sm:w-[233px] h-[56px] m-auto sm:m-0 mb-16 bg-bgSecondary text-textQuinary hover:bg-bgPrimary"
            onClick={submitRenewalOrder}
          >
            {props.fields.ButtonText.value}
          </Button>
        </div>
      </form>
    </div>
  );
};

export { RenewalOrderInfoContainer };
const Component = withDatasourceCheck()<RenewalOrderInfoContainerProps>(RenewalOrderInfoContainer);
export default aiLogger(Component, Component.name);

export const getServerSideProps: GetServerSideComponentProps = async (
  _rendering,
  _layoutData,
  context
) => {
  const { esiid } = context.query as QueryParamsMapType;
  const bpNumber = serverGetPartnerCookie(context);
  const access_token = context.req.session.user?.access_token;
  let getmeterReadDates = null;

  if (access_token && typeof access_token === 'string' && bpNumber !== null) {
    try {
      getmeterReadDates = await MyAccountAPI.getMeterReadDates(esiid, bpNumber, access_token);
      return {
        meterReadDates: getmeterReadDates?.data,
      };
    } catch (error: unknown) {
      return {
        redirect: '/oops',
      };
    }
  } else {
    return {
      redirect: '/oops',
    };
  }
};
