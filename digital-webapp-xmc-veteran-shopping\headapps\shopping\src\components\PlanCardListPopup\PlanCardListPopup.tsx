import { useEffect, useState } from 'react';
import { PlanBadge, PlanCardListProps } from 'components/ViewPlans/PlanCardList/PlanCardList';
import { Field, RichText, useSitecoreContext } from '@sitecore-jss/sitecore-jss-nextjs';
import PlanCard from 'components/PlanCard/PlanCard';
import { GetOffersBody, Offers, PlansGetResponse } from 'src/services/ViewOffersAPI/types';
import {
  getDefaultActiveBadgeColor,
  getDefaultActiveFontColor,
  getLowerDepositeBadge,
  getPlanBadge,
} from 'src/utils/getPlanBadge';
import { getPlanBadgeColor, getPlanFontColor } from 'src/utils/getPlanBadgeColor';
import { useRouter } from 'next/router';
import axios, { AxiosError } from 'axios-1.4';
import { CustomerIntent, DwellingType } from 'src/utils/query-params-mapping';
import { Loader } from '@mantine/core';
import { useAppSelector } from 'src/stores/store';
import { Product, ProductInfo } from 'src/services/GetProductDepositeAPI/types';
import { logErrorToAppInsights } from 'lib/app-insights-log-error';
import { localeMap } from 'src/utils/locale-mapping';

export interface PlanCardListPopupProps extends PlanCardListProps {
  popupHeader: Field<string>;
  popupDisclaimer: Field<string>;
  zipcode: string;
  isLDPlans?: boolean;
  setShowMismatchModal: React.Dispatch<React.SetStateAction<boolean>>;
  callback?: () => void;
}

export interface PlanCardResult {
  item: {
    PerkWhText: string;
    MonthToMonthTermText: string;
    FixedMonthTermText: string;
    SelectPlanButtonText: string;
    MobileNumber: string;
    ShowDetailsText: string;
    HideDetailsText: string;
    PriceByUsageText: string;
    MonthlyUsageText: string;
    AvgPricePerkWhText: string;
    HowMuchWillIUseText: string;
    ImportantPlanDetailTitle: string;
    ImportantPlanDetailsDescription: string;
    EarlyCancellationFeeText: string;
    ElectricityFactsLabelText: string;
    TermsOfServiceText: string;
    YourRightsAsaCustomerText: string;
    PlanCardFooterText: string;
    PlanBadgesList: BadgeList;
    DefaultActiveBadgeSingleFamilyWithoutPromoCodeBadgeColor: string;
    DefaultActiveBadgeMultiFamilyWithoutPromoCodeBadgeColor: string;
    DefaultActiveBadgeSingleFamilyWithoutPromoCodeFontColor: string;
    DefaultActiveBadgeMultiFamilyWithoutPromoCodeFontColor: string;
    BannerTitle: string;
    ZipcodeDescription: string;
    LowDepositebadge: string;
  };
}

interface BadgeList {
  jsonValue: PlanBadge[];
}

export interface PlanResults {
  plans: PlansGetResponse;
  planCardListDS: PlanCardResult;
}

const PlanCardListPopup = (props: PlanCardListPopupProps): JSX.Element => {
  //const { prom, dwel, zip, cint, bpNumber } = router.query as { [key: string]: string };
  const [data, setData] = useState<PlanResults>();
  const [ldData, setLdData] = useState<ProductInfo[]>();

  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  if (isPageEditing) return <div>PlanCardListPopup Component can be personalized</div>;

  const router = useRouter();

  let prom: string | undefined = undefined;
  let dwel: string | undefined = undefined;
  let zip: string | undefined = undefined;
  let cint: string | undefined = undefined;
  let bpNumber: string | undefined = undefined;
  let esiid: string | undefined = undefined;
  let authUser = undefined;
  if (!isPageEditing) {
    const query = router.query as { [key: string]: string };

    cint = query.cint;
    prom = query.prom;
    dwel = query.dwel;
    zip = query.zip;
    bpNumber = query.bpNumber;
    esiid = useAppSelector((state) => state.enrollment?.serviceInfo.esiid);
    authUser = useAppSelector((state) => state.authuser);
  }

  let plancount = 0;
  useEffect(() => {
    async function fetchNewPlans() {
      let promocode = prom;
      if (props.isLDPlans && dwel === '01') {
        const field = context.sitecoreContext?.route?.fields?.PromSingleFamily;
        if (field && 'value' in field) {
          promocode = field.value as string;
        }
      } else if (props.isLDPlans && dwel === '02') {
        const field = context.sitecoreContext?.route?.fields?.PromMultiFamily;
        if (field && 'value' in field) {
          promocode = field.value as string;
        }
      }

      if (promocode === '') {
        promocode = prom;
      }

      const offerBody: GetOffersBody = {
        customerIntent: CustomerIntent[cint ?? 'defaultIntent'], // provide a safe default key
        promoCode: promocode ?? '',
        dwellingType: DwellingType[dwel ?? 'defaultDwelling'], // provide a safe default key
        postalCode: parseInt(zip ?? '0'),
        language: localeMap[router.locale as string],
        channel: 'Web',
        esiid: esiid,
      };

      try {
        const req = await axios.post('/api/plans', { offerBody, locale: router.locale });
        if (props.isLDPlans) {
          const planIds: Product[] = [];
          req.data.plans.result.offers.map((plan: Offers) => {
            planIds.push({ product: plan.id });
          });

          const planDepositeBody = {
            partner: bpNumber,
            dwellingType: DwellingType[dwel ?? 'defaultDwelling'],
            esiid: esiid,
            productList: planIds,
          };
          const plansDepositsRes = await axios.post('/api/plans/plandeposits', planDepositeBody);
          if (plansDepositsRes && plansDepositsRes.data?.result) {
            setLdData(plansDepositsRes.data?.result);
            setData(req.data);
          } else props.setShowMismatchModal(false);
        } else {
          setData(req.data);
        }
      } catch (err) {
        const error = err as AxiosError;
        logErrorToAppInsights(error, {
          componentStack: 'PlancardListPopup',
        });
        console.log(error.toJSON());
        props.setShowMismatchModal(false);
      }
    }

    fetchNewPlans();
  }, [
    zip,
    cint,
    prom,
    dwel,
    authUser?.bpNumber,
    esiid,
    router,
    props,
    context.sitecoreContext?.route?.fields?.PromSingleFamily?.value,
    context.sitecoreContext?.route?.fields?.PromMultiFamily?.value,
    bpNumber,
  ]);

  const badgeList: PlanBadge[] = [];

  return (
    <>
      <RichText
        tag="p"
        className={`font-primaryBlack  text-base sm:text-plus2 -tracking-[0.25px] text-textPrimary md:text-plus3 md:-tracking-[0.5px] text-left mr-auto pr-[50px] sm:pr-0 ${
          props.isLDPlans ? 'max-w-610' : 'max-w-[750px]'
        }`}
        field={props.popupHeader}
      ></RichText>
      {data && data.plans ? (
        data.plans.result.offers.map((value: Offers) => {
          const planBadge = getPlanBadge(value.id, badgeList, prom as string);
          const PromoCodeBadgeColor = getDefaultActiveBadgeColor(value.id, badgeList);
          const DefaultActiveBadgeWithPromoCodeTextColor = getDefaultActiveFontColor(
            value.id,
            badgeList
          );

          const PlanBadgeColor = getPlanBadgeColor(
            dwel?.includes('01') ? true : false,
            prom as string,
            props.fields.DefaultActiveBadgeSingleFamilyWithoutPromoCodeBadgeColor.value,
            props.fields.DefaultActiveBadgeMultiFamilyWithoutPromoCodeBadgeColor.value,
            PromoCodeBadgeColor
          );

          const PlanBadgeFontColor = getPlanFontColor(
            dwel?.includes('01') ? true : false,
            prom as string,
            props.fields.DefaultActiveBadgeSingleFamilyWithoutPromoCodeFontColor.value,
            props.fields.DefaultActiveBadgeMultiFamilyWithoutPromoCodeFontColor.value,
            DefaultActiveBadgeWithPromoCodeTextColor
          );
          const lowDepositeBadge: string | null =
            (props.isLDPlans && getLowerDepositeBadge(value.id, ldData)) || null;
          const ldPrice = lowDepositeBadge && parseInt(lowDepositeBadge);

          //Adding plancount for RVEvent tracking
          plancount++;
          return (
            <div
              key={value.id}
              className={`${props.isLDPlans && ldPrice === 0 ? 'hidden' : 'block planModal'}`}
            >
              <PlanCard
                addWidth={true}
                plan={value}
                //planContents={data?.planContents[value.id]}
                variant="secondary"
                fields={props.fields}
                planCardBadge={planBadge}
                planCardBadgeColor={PlanBadgeColor ? PlanBadgeColor : 'bg-bgVigintiunary'}
                planCardFontColor={PlanBadgeFontColor ? PlanBadgeFontColor : 'text-textQuinary'}
                ldBadge={lowDepositeBadge ? 'Low Deposit - $' + ldPrice : undefined}
                ldPrice={ldPrice as number}
                callback={() => {
                  if (props.callback) props.callback();
                }}
                position={plancount}
              />
            </div>
          );
        })
      ) : (
        <Loader />
      )}
      <RichText
        tag="p"
        className="font-primaryRegular text-textQuattuordenary text-sm"
        field={props.popupDisclaimer}
      ></RichText>
    </>
  );
};

export default PlanCardListPopup;
