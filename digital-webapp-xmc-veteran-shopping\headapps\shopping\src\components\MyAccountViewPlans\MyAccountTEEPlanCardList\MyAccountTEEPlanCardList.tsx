import {
  Field,
  GetServerSideComponentProps,
  LinkField,
  Placeholder,
  useComponentProps,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import PlanCard from 'components/PlanCard/PlanCard';
import PlanCardSort from 'components/ViewPlans/PlanCardSort/PlanCardSort';
import { useI18n } from 'next-localization';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import useSort from 'src/hooks/useSort';
import FetchPlansAPI from 'src/services/FetchPlansAPI';
import { Plan } from 'src/services/FetchPlansAPI/types';
import MyAccountAPI from 'src/services/MyAccountAPI';
import { ExistingPlanResponse, ProductRateListResponse } from 'src/services/MyAccountAPI/types';
import { Offers, PlansGetResponse } from 'src/services/ViewOffersAPI/types';
import { setCorrelationId, setSessionId } from 'src/stores/enrollmentSlice';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import { serverGetPartnerCookie } from 'src/utils/authTokenUtil';
import {
  getDefaultActiveBadgeColor,
  getDefaultActiveFontColor,
  getPlanBadge,
} from 'src/utils/getPlanBadge';
import { getPlanBadgeColor, getPlanFontColor } from 'src/utils/getPlanBadgeColor';
import { QueryParamsMapType } from 'src/utils/query-params-mapping';
import { v4 } from 'uuid';
import { formatString } from 'src/utils/formatString';
import { AxiosError } from 'axios';
import { logErrorToAppInsights } from 'lib/app-insights-log-error';
import { setKYCPlan } from 'src/stores/planSlice';
import dayjs from 'dayjs';
import { PlanColors, WebCodeTFNNumber } from 'components/ViewPlans/PlanCardList/PlanCardList';
import axios from 'axios-1.4';
import { Loader } from '@mantine/core';
import { useInterstitials } from 'src/hooks/modalhooks';
import { getPlanIncentives } from 'src/utils/getIncentives';

export interface PlanResults {
  plans: PlansGetResponse;
  correlationid: string;
  sessionid?: string;
  existingPlan: ExistingPlanResponse | null;
  existingPlanContent: Plan | null;
  existingPlanRate: ProductRateListResponse | null;
  access_token: string;
  bpNumber: string;
  locale: string;
}
export interface Incetives {
  fields: {
    IncentiveID: Field<string>;
    IncentiveSpecialOfferText: Field<string>;
    IncentiveDisclaimerText: Field<string>;
  };
}

export interface PlanBadge {
  fields: {
    RootPlanId: Field<string>;
    DefaultActiveBadge: Field<string>;
    PromoCodeBadge: Field<string>;
    DefaultActiveBadgeWithPromoCodeBadgeColor: Field<string>;
    DefaultActiveBadgeWithPromoCodeTextColor: Field<string>;
  };
}

export interface SuggestionType {
  fields: {
    Name: Field<string>;
    SortKey: Field<keyof Offers>;
    SortDirection: Field<'asc' | 'desc'>;
  };
}

export interface PlanCardListProps extends Partial<ComponentProps> {
  fields: {
    PerkWhText: Field<string>;
    MonthToMonthTermText: Field<string>;
    FixedMonthTermText: Field<string>;
    SelectPlanButtonText: Field<string>;
    MobileNumber: Field<string>;
    ShowDetailsText: Field<string>;
    HideDetailsText: Field<string>;
    PriceByUsageText: Field<string>;
    MonthlyUsageText: Field<string>;
    AvgPricePerkWhText: Field<string>;
    HowMuchWillIUseText: Field<string>;
    ImportantPlanDetailTitle: Field<string>;
    ImportantPlanDetailsDescription: Field<string>;
    EarlyCancellationFeeText: Field<string>;
    EarlyCancellationFeeToolTip: Field<string>;
    ElectricityFactsLabelText: Field<string>;
    TermsOfServiceText: Field<string>;
    YourRightsAsaCustomerText: Field<string>;
    PlanCardFooterText: Field<string>;
    PlanBadgesList: PlanBadge[];
    DefaultActiveBadgeSingleFamilyWithoutPromoCodeBadgeColor: Field<string>;
    DefaultActiveBadgeMultiFamilyWithoutPromoCodeBadgeColor: Field<string>;
    DefaultActiveBadgeSingleFamilyWithoutPromoCodeFontColor: Field<string>;
    DefaultActiveBadgeMultiFamilyWithoutPromoCodeFontColor: Field<string>;
    BannerTitle: Field<string>;
    ZipcodeDescription: Field<string>;
    ZipcodeHeader: Field<string>;
    OrderInformationPageLink: LinkField;
    SortByTypes: SuggestionType[];
    AvailablePlansText: Field<string>;
    CurrentPlanEFLLink: LinkField;
    CurrentPlanTOSLink: LinkField;
    CurrentPlanYRACLink: LinkField;
    SortByLabel: Field<string>;
    ShowCurrentPlan: Field<boolean>;
    NeedHelpText: Field<string>;
    NeedHelpPhNumber: Field<string>;
    EnergyRateText: Field<string>;
    AvgRateText: Field<string>;
    ECFText: Field<string>;
    ECFRemainingText: Field<string>;
    BaseChargeText: Field<string>;
    BaseChargeValue: Field<string>;
    EnergyCharge: Field<string>;
    EnergyChargeValue: Field<string>;
    ECFPricingText: Field<string>;
    kWhText: Field<string>;
    SideBarColorsForPlans: PlanColors[];
    TEETFNNumbersForWebcode: WebCodeTFNNumber[];
    AveragePriceDescription: Field<string>;
    IncentivesList: Incetives[];
    DigitalDiscountText: Field<string>;
    DigitalDiscountTooltipText: Field<string>;
  };
}
let dispatch: ReturnType<typeof useAppDispatch>;
const MyAccountPlanCardList = (props: PlanCardListProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let bpNumber = undefined;
  let existingPlan = undefined;
  let transferServiceInfo = undefined;

  if (!isPageEditing) {
    bpNumber = useAppSelector((state) => state.authuser?.bpNumber);
    existingPlan = useAppSelector((state) => state.authuser?.currentPlan);
    transferServiceInfo = useAppSelector((state) => state.transfer?.personalInfo);
    dispatch = useAppDispatch();
  }
  const primaryCardSize = 3;
  const router = useRouter();
  const { cint, prom, dwel, zip, tdsp, esiid, contractaccount } =
    router.query as QueryParamsMapType;
  const { locale } = useI18n();
  const data = useComponentProps<PlanResults>(props.rendering?.uid);
  const access_token = data?.access_token;
  const sessionId = data?.sessionid;

  const [planData, setPlanData] = useState<PlanResults | undefined>();

  const [istoggelCheck, setToggelCheck] = useState(true);

  let modifiedPlan: Offers | null = null;
  useInterstitials(true);

  if (data && data.existingPlan) {
    if (!isPageEditing) {
      dispatch(
        setKYCPlan({
          CONTRACT_NO: data.existingPlan.result.CONTRACT_NO,
          ESIID: data.existingPlan.result.ESIID,
          PRODUCT_ID: data.existingPlan.result.PRODUCT_ID,
          PRODUCT: data.existingPlan.result.PRODUCT,
          PRICE: data.existingPlan.result.PRICE,
          TERMUNIT: data.existingPlan.result.TERMUNIT,
          TERMMONTHCOUNT: data.existingPlan.result.TERMMONTHCOUNT,
          USAGE: data.existingPlan.result.USAGE,
          TERM_EXP_DATE: data.existingPlan.result.TERM_EXP_DATE,
          RATE: [],
        })
      );
    }
    const price: { AverageMonthlyUse: number; AveragePriceperkWh: number }[] = [];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data.existingPlan.result?.pricingByUsage?.map((rate: any) => {
      rate.usage !== 0 &&
        price.push({ AverageMonthlyUse: rate.usage, AveragePriceperkWh: rate.price });
    });

    const teeProductRateDetails: {
      value: string;
      conditionType: string;
      description: string;
      range: string;
    }[] = [];

    const eflLink = props.fields.CurrentPlanEFLLink.value.href as string;
    const tosLink = props.fields.CurrentPlanTOSLink.value.href as string;
    const yracLink = props.fields.CurrentPlanYRACLink.value.href as string;

    modifiedPlan = {
      id: data.existingPlan.result.PRODUCT_ID,
      name: data.existingPlan.result.PRODUCT,
      baseRate: parseFloat(data.existingPlan.result.PRICE),
      term: data.existingPlan.result.TERMMONTHCOUNT,
      campaignId: '',
      cancellationFee: '',
      termsOfServiceLink: formatString(tosLink, [
        data.existingPlan.result.PRODUCT_ID,
        locale(),
        'TermsOfServiceAgreement',
        cint as string,
        tdsp && tdsp.includes('_') ? tdsp.split('_', 2)[1] : tdsp ?? '',
      ]),
      yourRightsLink: formatString(yracLink, [
        data.existingPlan.result.PRODUCT_ID,
        locale(),
        'YourRightsAsCustomer',
        cint as string,
        tdsp && tdsp.includes('_') ? tdsp.split('_', 2)[1] : tdsp ?? '',
      ]),
      electricityFactsLink: formatString(eflLink, [
        data.existingPlan.result.PRODUCT_ID,
        locale(),
        'EnergyFactsLabel',
        cint as string,
        tdsp && tdsp.includes('_') ? tdsp.split('_', 2)[1] : tdsp ?? '',
      ]),
      oneLineSummary: '',
      disclosureStatementLink: '',
      incentiveId: '',
      incentiveDisclaimer: '',
      incentiveSpecialOfferText: '',
      priority: 0,
      totalGreenUp: false,
      percentGreen: '',
      rates: price,
      ev: '',
      enrollDate: dayjs(new Date()).toISOString(),
      planBenefits: '',
      planDisclaimer: '',
      trieProductRateDetails: teeProductRateDetails,
      rateType: '',
    };
  }

  useEffect(() => {
    const getCustomizedPlans = async () => {
      await axios.get('/api/myaccount/offers/eyeballdata', {
        params: {
          contractaccount: contractaccount,
          bpNumber: bpNumber,
          esiid: esiid,
          session_id: sessionId,
        },
      });
    };
    const fetchData = async () => {
      const plans = await axios.get('/api/myaccount/offers/fetchplans', {
        params: {
          cint: cint,
          locale: data?.locale,
          dwel: dwel,
          zip: zip,
          tdsp: tdsp,
          sessionId: sessionId,
          prom: prom,
          bpNumber: bpNumber,
          esiid: esiid,
        },
      });
      setPlanData(plans.data);
    };
    if (access_token !== '' && bpNumber) {
      getCustomizedPlans();
      fetchData();
    }
  }, [access_token, bpNumber, data?.locale]);

  // workaround for when planData is not updated when switching locale
  // useEffect(() => {
  //   setPlanData(data);
  // }, [data]);

  const offers =
    !planData || planData?.plans?.result?.offers != undefined
      ? planData?.plans?.result?.offers
      : [];

  if (planData && planData.correlationid)
    if (!isPageEditing) {
      dispatch(setCorrelationId(planData?.correlationid));
    }
  if (planData && planData.sessionid)
    if (!isPageEditing) {
      dispatch(setSessionId(planData?.sessionid));
    }

  const { sortedData, setSortKey, setSortDirection } = useSort<Offers>(offers as Offers[], {
    sortDirection: 'asc',
    sortKey: 'priority',
  });

  if (planData === undefined) {
    return (
      <Loader
        className="flex justify-center min-h-[700px] w-full m-auto ml-0 relative left-[50%] wide:ml-0 ipad:ml-0 ipad:pl-6 wide:pl-6"
        size="lg"
      />
    );
  } else {
    return (
      <div className="plans bg-transparent sm:ml-[350px] wide:ml-0 ipad:ml-0 ipad:pl-6 wide:pl-6 pb-0 sm:pb-8 min-h-[800px] pl-0">
        <div className="flex flex-col w-full">
          <div className="flex flex-col gap-4 sm:gap-[32px] items-center sm:items-start mt-4 sm:mt-[30px] w-full max-w-[932px]  sm:pl-[70px] sm:px-0 px-6 my-8 mb-0 ipad:pl-[15px] ipad:pr-[15px]">
            <div className="bg-transparent w-full px-0 sm:px-0 pl-0 sm:pl-0 relative sm:mt-0">
              <div className="col-6 flex sm:flex-row flex-col sm:max-w-[792px] sm:items-center">
                <div className="max-w-full sm:max-w-[592px] flex-col sm:items-start gap-[8px] sm:gap-[32px] flex flex-1 pl-[16px] sm:pl-0 mb-[8px]">
                  <div className="text-textQuattuordenary text-base font-primaryBold flex-row flex gap-2 visible items-center mt-[8px]">
                    <p className="font-primaryBold text-textQuattuordenary text-minus1 sm:mt-0 mt-5  sm:text-base">
                      {props?.fields?.AvailablePlansText?.value}
                    </p>
                  </div>
                </div>
                <div className="m-auto sm:m-0 flex-1 w-full">
                  <PlanCardSort
                    AvailablePlansText={props?.fields?.AvailablePlansText?.value}
                    SortByLabel={props.fields.SortByLabel.value}
                    setSortKey={setSortKey}
                    setSortDirection={setSortDirection}
                    Suggestions={props.fields?.SortByTypes}
                    hideDiscountToggle={true}
                    setToggelCheck={setToggelCheck}
                    istoggelCheck={istoggelCheck}
                  />
                </div>
              </div>
              <div className="block sm:flex flex-row mt-[30px] max-w-1216 wide:block ipad:block">
                <div className="flex flex-col gap-[40px] sm:gap-[32px] w-full">
                  {sortedData &&
                    sortedData.length > 0 &&
                    sortedData.slice(0, primaryCardSize).map((value) => {
                      const planBadge = getPlanBadge(
                        value.id,
                        props?.fields?.PlanBadgesList,
                        prom as string
                      );
                      const PromoCodeBadgeColor = getDefaultActiveBadgeColor(
                        value.id,
                        props?.fields?.PlanBadgesList
                      );
                      const DefaultActiveBadgeWithPromoCodeTextColor = getDefaultActiveFontColor(
                        value.id,
                        props?.fields?.PlanBadgesList
                      );

                      const PlanBadgeColor = getPlanBadgeColor(
                        dwel?.includes('01') ? true : false,
                        prom as string,
                        props?.fields?.DefaultActiveBadgeSingleFamilyWithoutPromoCodeBadgeColor
                          .value,
                        props?.fields?.DefaultActiveBadgeMultiFamilyWithoutPromoCodeBadgeColor
                          .value,
                        PromoCodeBadgeColor
                      );

                      const PlanBadgeFontColor = getPlanFontColor(
                        dwel?.includes('01') ? true : false,
                        prom as string,
                        props?.fields?.DefaultActiveBadgeSingleFamilyWithoutPromoCodeFontColor
                          .value,
                        props?.fields?.DefaultActiveBadgeMultiFamilyWithoutPromoCodeFontColor.value,
                        DefaultActiveBadgeWithPromoCodeTextColor
                      );

                      const redirectionCallback = (): void => {
                        router.push({
                          pathname: props.fields?.OrderInformationPageLink?.value?.href,
                          query: {
                            ...router.query,
                            planid: value.id,
                            currentplanid: value.id,
                            rdcampaign: false,
                          },
                        });
                      };
                      const planIncentives = getPlanIncentives(
                        value.incentiveId,
                        props.fields.IncentivesList
                      );
                      return (
                        <div key={value.id}>
                          <PlanCard
                            plan={value}
                            //planContents={planData?.planContents[value.id]}
                            variant="tee"
                            fields={props?.fields}
                            myAccountPlanCardList={true}
                            planCardBadgeColor={
                              PlanBadgeColor ? PlanBadgeColor : 'bg-bgVigintiunary'
                            }
                            planCardFontColor={
                              PlanBadgeFontColor ? PlanBadgeFontColor : 'text-textQuinary'
                            }
                            showLoader={true}
                            ldBadge={planBadge}
                            callback={redirectionCallback}
                            PlanIncentiveSpecialOfferText={
                              planIncentives.length > 0 ? planIncentives[0] : ''
                            }
                            PlanIncentiveDisclaimerText={
                              planIncentives.length > 0 ? planIncentives[1] : ''
                            }
                            istoggelCheck={istoggelCheck}
                          />
                        </div>
                      );
                    })}
                </div>
              </div>
              <div className="block sm:flex flex-row mt-[30px] max-w-1216 wide:block ipad:block">
                <div className="flex flex-col gap-[40px] sm:gap-[32px]">
                  {props.rendering && (
                    <Placeholder name="jss-comparenewhome" rendering={props.rendering} />
                  )}
                </div>
              </div>
              <div className="block sm:flex flex-row mt-[30px] max-w-1216 wide:block ipad:block">
                <div className="flex flex-col gap-[40px] sm:gap-[32px] w-full">
                  {sortedData &&
                    sortedData.length > 0 &&
                    sortedData.slice(3, sortedData.length).map((value) => {
                      const redirectionCallback = (): void => {
                        router.push({
                          pathname: props.fields?.OrderInformationPageLink?.value?.href,
                          query: {
                            ...router.query,
                            planid: value.id,
                            currentplanid: value.id,
                            rdcampaign: false,
                          },
                        });
                      };
                      return (
                        <PlanCard
                          plan={value}
                          key={value.id}
                          variant="tee"
                          fields={props?.fields}
                          planCardBadgeColor={'bg-bgVigintiunary'}
                          showLoader={true}
                          callback={redirectionCallback}
                          myAccountPlanCardList={true}
                          istoggelCheck={istoggelCheck}
                        />
                      );
                    })}
                  {cint === '2' &&
                    existingPlan?.plan?.id &&
                    transferServiceInfo?.oldtdsp === transferServiceInfo?.newServiceAddress.tdsp ? (
                    <PlanCard
                      plan={existingPlan.plan}
                      //planContents={existingPlan.contents}
                      variant="secondary"
                      fields={props?.fields}
                      showLoader={true}
                      planCardBadge="Current Plan"
                      planCardBadgeColor="bg-violet"
                      planCardFontColor="text-textQuinary"
                      myAccountPlanCardList={true}
                      ldBadge="Current Plan"
                      istoggelCheck={istoggelCheck}
                      callback={() => {
                        router.push({
                          pathname: props.fields?.OrderInformationPageLink?.value?.href,
                          query: {
                            ...router.query,
                            planid: existingPlan.plan.id,
                            currentplanid: existingPlan.plan.id,
                            rdcampaign: false,
                          },
                        });
                      }}
                    />
                  ) : (
                    <></>
                  )}
                  {cint === '3' && data && props.fields.ShowCurrentPlan.value && (
                    <PlanCard
                      plan={modifiedPlan as Offers}
                      //planContents={data.existingPlanContent as Plan}
                      variant="secondary"
                      fields={props?.fields}
                      showLoader={true}
                      planCardBadge="Keep My Plan"
                      planCardBadgeColor="bg-violet"
                      planCardFontColor="text-textQuinary"
                      myAccountPlanCardList={true}
                      ldBadge="Keep My Plan"
                      buttonText="Renew Now"
                      istoggelCheck={istoggelCheck}
                      callback={(): void => {
                        router.push({
                          pathname: props.fields?.OrderInformationPageLink?.value?.href,
                          query: {
                            ...router.query,
                            planid: modifiedPlan?.id,
                            currentplanid: modifiedPlan?.id,
                            rdcampaign: false,
                          },
                        });
                      }}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
};

export const getServerSideProps: GetServerSideComponentProps = async (
  _rendering,
  _layoutData,
  context
) => {
  const cint = context.query.cint as keyof QueryParamsMapType;
  const sessionid = v4();
  const contractaccount = context.query.contractaccount as string;
  const esiid = context.query.esiid as string;
  const access_token = context.req.session.user?.access_token;
  const bpNumber = serverGetPartnerCookie(context);
  const locale = context.locale as string;
  if (access_token && bpNumber) {
    if (cint === '3') {
      await MyAccountAPI.getExistingPlan(bpNumber, contractaccount, esiid, access_token).then(
        async (response) => {
          return {
            existingPlan: cint === '3' ? response?.data : null,
            existingPlanContent: null,
            existingPlanRate: null,
            sessionid,
            access_token,
            bpNumber,
            locale,
          };
        }
      );
    }
    return {
      existingPlan: null,
      existingPlanContent: null,
      existingPlanRate: null,
      sessionid,
      access_token,
      bpNumber,
      locale,
    };
  } else {
    const queryParams = {
      errorCode: 'My Account Plan Card List - AuthToken Not available',
    };
    let err;
    const error = err as unknown as AxiosError;
    logErrorToAppInsights(error, {
      componentStack: 'My Account Plan Card List - AuthToken Not available' + Date.now(),
    });
    return {
      redirect: '/oops?' + queryParams.errorCode,
    };
  }
};

export default aiLogger(MyAccountPlanCardList, MyAccountPlanCardList.name);
