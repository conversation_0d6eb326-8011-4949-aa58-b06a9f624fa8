import {
  Text,
  Field,
  withDatasourceCheck,
  useComponentProps,
  GetServerSideComponentProps,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import MyAccountAPI from 'src/services/MyAccountAPI';
import { ExistingPlanResponse, GetUsageOverViewResponse } from 'src/services/MyAccountAPI/types';
import { useAppSelector } from 'src/stores/store';
import { serverGetPartnerCookie } from 'src/utils/authTokenUtil';
import { QueryParamsMapType } from 'src/utils/query-params-mapping';

type CurrentPlanAndUsageProps = ComponentProps & {
  fields: {
    CurrentPlanAndUsageTitle: Field<string>;
    AverageMonthlyUsageTitle: Field<string>;
    HidePlanUsageKWh: Field<boolean>;
    HideAvgUsageKWh: Field<boolean>;
  };
};

interface CurrentPlan {
  ContractNumber: string;
  PlanName: string;
  PlanID: string;
  Price: number;
  Term: number;
  Usage: number;
  Term_Exp_Date: string;
  Rate: { AverageMonthlyUse: number; AveragePriceperkWh: number };
}

interface MyUsageData {
  esiid: string;
  totalUsage: number;
  numOfMonths: number;
  avgMonthlyUsage: number;
  sqFootage: number;
}
interface CurrentPlanData {
  plan: ExistingPlanResponse;
  usageOverview: GetUsageOverViewResponse;
}

const CurrentPlanAndUsage = (props: CurrentPlanAndUsageProps): JSX.Element => {
   const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let plan = undefined;
  if(!isPageEditing) {
     plan = useAppSelector((state) => state.plans?.KYCPlan);
  }
  const plandata: CurrentPlan = {
    ContractNumber: '',
    PlanName: '',
    PlanID: '',
    Price: 0,
    Term: 0,
    Usage: 0,
    Term_Exp_Date: '',
    Rate: {
      AverageMonthlyUse: 0,
      AveragePriceperkWh: 0,
    },
  };

  const myUsageData: MyUsageData = {
    esiid: '',
    totalUsage: 0,
    numOfMonths: 0,
    avgMonthlyUsage: 0,
    sqFootage: 0,
  };
  const data = useComponentProps<CurrentPlanData>(props.rendering.uid);
  if (data?.plan !== null && data?.plan !== undefined) {
    plandata.ContractNumber = data.plan.result.CONTRACT_NO;
    plandata.PlanID = data.plan.result.PRODUCT_ID;
    plandata.PlanName = data.plan.result.PRODUCT;
    plandata.Price = parseFloat(data.plan.result.PRICE);
    plandata.Term = data.plan.result.TERMMONTHCOUNT;
    plandata.Usage = parseInt(data.plan.result.USAGE);
    plandata.Term_Exp_Date = data.plan.result.TERM_EXP_DATE;
    plandata.Rate.AverageMonthlyUse = parseInt(data.plan.result.USAGE);
    plandata.Rate.AveragePriceperkWh = parseFloat(data.plan.result.PRICE);
  } else {
    plandata.ContractNumber = plan?.CONTRACT_NO;
    plandata.PlanID = plan?.PRODUCT_ID;
    plandata.PlanName = plan?.PRODUCT;
    plandata.Price = parseFloat(plan?.PRICE);
    plandata.Term = plan?.TERMMONTHCOUNT;
    plandata.Usage = parseInt(plan?.USAGE);
    plandata.Term_Exp_Date = plan?.TERM_EXP_DATE;
    plandata.Rate.AverageMonthlyUse = parseInt(plan?.USAGE);
    plandata.Rate.AveragePriceperkWh = parseFloat(plan?.PRICE);
  }

  if (
    data?.usageOverview !== null &&
    data?.usageOverview !== undefined &&
    data?.usageOverview.result.length > 0
  ) {
    myUsageData.esiid = data?.usageOverview.result[0].esiid;
    myUsageData.totalUsage = data?.usageOverview.result[0].totalUsage;
    myUsageData.numOfMonths = data?.usageOverview.result[0].numOfMonths;
    myUsageData.avgMonthlyUsage = data?.usageOverview.result[0].avgMonthlyUsage;
    myUsageData.sqFootage = data?.usageOverview.result[0].sqFootage;
  }

  console.log('plandata=',plandata);
    console.log('data=',data);
        console.log('plan=',plan);

  return (
    <div className="w-full my-5 md:items-start pl-5 pr-5 md:mt-[30px] mt-[30px] ml-0 sm:pl-0 sm:ml-[410px] wide:ml-0 ipad:ml-0 ipad:pl-6 wide:pl-6">
      <Text
        tag="p"
        className="font-primaryBlack text-xl text-center sm:text-plus3  sm:text-left text-textQuattuordenary"
        field={props.fields.CurrentPlanAndUsageTitle}
      />
      {/* fix */}
      <div className="sm:flex sm:flex-row text-center sm:text-left gap-1 items-center md:items-start pt-5">
        <Text
          tag="p"
          className="font-primaryRegular text-textQuattuordenary text-minus2 font-bold"
          field={{
            value: `${plandata.PlanName}`,
          }}
        />
        <Text
          tag="p"
          className="font-primaryRegular text-textQuattuordenary text-minus2"
          field={{
            value: `| ${plandata.Term} Months - Fixed | ${plandata.Price}¢ per kWh`,
          }}
        />
        {!props.fields.HidePlanUsageKWh.value && !isNaN(plandata.Usage) ? (
          <Text
            tag="p"
            className="font-primaryRegular text-textQuattuordenary text-minus2"
            field={{
              value: `at ${plandata.Usage} kWh`,
            }}
          />
        ) : (
          ''
        )}
      </div>
      {!props.fields.HideAvgUsageKWh.value && myUsageData?.avgMonthlyUsage > 0 ? (
        <div className="sm:flex sm:flex-row gap-1 pt-3 items-center text-center sm:text-left">
          <Text
            tag="p"
            className="font-primaryBlack text-textPrimary text-2xl sm:text-[45px] font-bold"
            field={{ value: `${myUsageData?.avgMonthlyUsage} kWh` }}
          />
          <Text
            tag="p"
            className="font-primaryRegular text-textQuattuordenary text-sm  sm:pl-2"
            field={props.fields.AverageMonthlyUsageTitle}
          />
        </div>
      ) : (
        ''
      )}
    </div>
  );
};

export { CurrentPlanAndUsage };
const Component = withDatasourceCheck()<CurrentPlanAndUsageProps>(CurrentPlanAndUsage);
export default aiLogger(Component, Component.name);

export const getServerSideProps: GetServerSideComponentProps = async (
  _rendering,
  _layoutData,
  context
) => {
  const { contractaccount, esiid, currentPlanID } = context.query as QueryParamsMapType;
  const bpNumber = serverGetPartnerCookie(context);
  const access_token = context.req.session.user?.access_token;

  let existingPlan = null;
  let myUsage = null;

  if (access_token && typeof access_token === 'string') {
    try {
      // get partner number from authtoken cookie
      if (currentPlanID === '' || currentPlanID === undefined) {
        if (bpNumber !== null) {
          existingPlan = await MyAccountAPI.getExistingPlan(
            bpNumber,
            contractaccount,
            esiid,
            access_token
          );
        }
      }
      if (esiid != '') {
        myUsage = await MyAccountAPI.getUsageOverview(esiid, access_token);
      }

      return {
        plan: currentPlanID ? null : existingPlan?.data,
        usageOverview: myUsage?.data,
      };
    } catch (error: unknown) {
      return {
        redirect: '/oops',
      };
    }
  } else {
    return {
      redirect: '/oops',
    };
  }
};
