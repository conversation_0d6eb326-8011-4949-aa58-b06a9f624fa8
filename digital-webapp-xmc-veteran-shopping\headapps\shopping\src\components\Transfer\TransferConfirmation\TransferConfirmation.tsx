import {
  Field,
  LinkField,
  Placeholder,
  RichText,
  Text,
  withDatasourceCheck,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { AxiosError, AxiosResponse } from 'axios';
import axios from 'axios-1.4';
import Button from 'components/Elements/Button/Button';
import PrintPageButton from 'components/Elements/PrintPageButton/PrintPageButton';
import InfoText from 'components/common/InfoText/InfoText';
import { logErrorToAppInsights } from 'lib/app-insights-log-error';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import { useRef, useState } from 'react';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useLoader } from 'src/hooks/modalhooks';
import { useNavigationDirection } from 'src/hooks/useNavigationDirection';
import {
  OrderAddonProductRequest,
  OrderAddonProductResponse,
  OrderedAddonProduct,
} from 'src/services/AddOnProductAPI/types';
import { setOrderedPlans } from 'src/stores/planSlice';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import { DwellingType, QueryParamsMapType } from 'src/utils/query-params-mapping';
import PageBuilder from 'src/components/PageBuilder/PageBuilder';


type TransferConfirmationProps = ComponentProps & {
  fields: {
    ConfirmationTitle: Field<string>;
    ConfirmationNumberLabel: Field<string>;
    AccountNumberLabel: Field<string>;
    PrintPageLabel: Field<string>;
    OrderInformationTitle: Field<string>;
    CurrentPlanLabel: Field<string>;
    CurrentESIIDLabel: Field<string>;
    CurrentAddressLabel: Field<string>;
    CurrentServiceEndDateLabel: Field<string>;
    NewPlanLabel: Field<string>;
    NewESIIDLabel: Field<string>;
    NewAddressLabel: Field<string>;
    NewServiceStartDateLabel: Field<string>;
    OrderInformationDescription: Field<string>;
    AccountSummaryButtonText: Field<string>;
    OrderProductAndGoToAccountSummary: Field<string>;
    ResidentialMyAccountPageUrl: LinkField;
  };
};

const TransferConfirmation = (props: TransferConfirmationProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  if (isPageEditing) return <PageBuilder componentName="Fields of TransferConfirmation" />;
  let planDetails = undefined;
  let transferDetails = undefined;
  let authUser = undefined;
  let dispatch: ReturnType<typeof useAppDispatch>;
  if (!isPageEditing) {
    planDetails = useAppSelector((state) => state.plans);
    transferDetails = useAppSelector((state) => state.transfer);
    authUser = useAppSelector((state) => state.authuser);
    dispatch = useAppDispatch();
  }
  useNavigationDirection();
  const router = useRouter();
  const { dwel } = router.query as QueryParamsMapType;
  const printRef = useRef<HTMLDivElement>(null);
  const KYCPlanPrice = parseFloat(planDetails.KYCPlan.PRICE);
  const SelectedPlanRate = planDetails.selectedPlan.rate * 100;
  const [isOrderCompleted, setIsOrderCompleted] = useState(false);
  const { openModal } = useLoader();

  async function redirectToNextPage() {
    if (props.fields?.ResidentialMyAccountPageUrl?.value?.href !== '') {
      openModal();
      const decodedUri = decodeURIComponent(
        props.fields.ResidentialMyAccountPageUrl?.value.href as string
      );
      router.push({
        pathname: decodedUri,
        query: {
          clearcache: true,
        },
      });
    }
  }

  const orderAddonProducts = async () => {
    try {
      const addonPlanKeys = Object.keys(planDetails.addonPlans);

      if (addonPlanKeys && addonPlanKeys.length !== 0) {
        const selectedAddons: OrderedAddonProduct[] = addonPlanKeys.map((key) => {
          return {
            productId: planDetails.addonPlans[key].planId,
            quantity: 1,
          };
        });

        const orderAddonReq = await axios.post<
          OrderAddonProductResponse,
          AxiosResponse<OrderAddonProductResponse>,
          OrderAddonProductRequest
        >(
          '/api/myaccount/plans/addonplans',
          {
            businessPartnerNumber: authUser.bpNumber,
            contractAccountNumber: planDetails.KYCPlan.CONTRACT_NO,
            esiid: transferDetails.personalInfo.newServiceAddress.esiid,
            zipCode: transferDetails.personalInfo.newServiceAddress.zip,
            currentPlanId: planDetails.selectedPlan.planId,
            dwellingType: DwellingType[dwel as string],
            language: 'English',
            productOrders: selectedAddons,
          },
          { headers: { 'Content-Type': 'application/json' } }
        );
        setIsOrderCompleted(true);
        if (!isPageEditing) {
          dispatch(setOrderedPlans(orderAddonReq.data.result));
        }
        redirectToNextPage();
        return;
      }
      redirectToNextPage();
    } catch (err) {
      const error = err as AxiosError;
      logErrorToAppInsights(error, {
        componentStack: 'My Account Place Order Transfer - orderAddonProducts',
      });
      const queryParams = {
        errorCode: 'My Account Place Order Transfer - orderAddonProducts',
      };
      router.push({
        pathname: '/oops',
        query: { ...queryParams },
      });
    }
  };

  return (
    <div className="flex flex-col gap-8 sm:px-0 md:gap-4 md:w-[864px] w-full md:max-w-[732px] wide:w-full ipad:w-full ipad:px-[20px] sm:pl-0 wide:px-0">
      <div className="enrollment-confirmation print:m-8 print:w-11/12  block">
        <div className="flex flex-col p-5 gap-5  md:gap-4 px-[20px] sm:px-0 ">
          <div className="flex flex-row md:items-center">
            <Text
              tag="p"
              className="font-primaryBold text-textQuattuordenary md:flex-grow md:text-plus3 text-plus1 sm:text-plus2 break-words max-w-[780px] pr-4"
              field={props.fields.ConfirmationTitle}
            />
          </div>
          <div className="flex flex-row md:items-center">
            <Text
              tag="p"
              className="font-primaryRegular text-textQuattuordenary text-minus1 sm:text-base text-left break-normal"
              field={{
                value: props.fields.ServiceReceivedMessage.value.replace(
                  ' ${ConfirmId}',
                  `${' '} ${transferDetails.transferInfo.serviceContractNumber}`
                ),
              }}
            />
          </div>
          <div className="flex flex-col gap-2 md:gap-3">
            <InfoText
              label={props.fields.NewAddressLabel?.value}
              value={transferDetails.personalInfo.newServiceAddress.display_text}
              isRow={false}
              showColon={true}
            />
            <InfoText
              label={props.fields.NewESIIDLabel?.value}
              value={transferDetails.personalInfo.newServiceAddress.esiid}
              isRow={false}
              showColon={true}
            />
            <div className="flex flex-col">
              <p className="font-primaryBold text-textQuattuordenary text-minus1 sm:text-base break-words w-[180px] md:w-[210px]">
                {props.fields.NewPlanLabel?.value}
              </p>
              <div className="flex flex-col">
                <span className="font-primaryRegular text-textQuattuordenary text-minus1 ">
                  {planDetails.selectedPlan.planName}
                </span>
                <span className="font-primaryRegular  text-textQuattuordenary text-minus1">
                  {planDetails.selectedPlan?.term === 0
                    ? 'Month-to-Month'
                    : planDetails.selectedPlan.term + ' Months - Fixed'}{' '}
                  | {SelectedPlanRate.toFixed(2) + '¢' + ' per kWh'}
                </span>
              </div>
            </div>
            <InfoText
              label={props.fields.NewServiceStartDateLabel?.value}
              value={transferDetails.transferInfo.serviceStartDate}
              isRow={false}
              showColon={true}
            />
          </div>
          <hr className="border-t-1 border-borderVigintiternary border-solid w-full max-w-[800px] px-[15px] border-[1px] block m-0 my-[20px]" />
          <div className="flex flex-col gap-2 md:gap-3">
            <InfoText
              label={props.fields.CurrentAddressLabel?.value}
              value={transferDetails.personalInfo.oldServiceAddress}
              isRow={false}
              showColon={true}
            />
            <InfoText
              label={props.fields.CurrentESIIDLabel?.value}
              value={transferDetails.personalInfo.oldEsiid}
              isRow={false}
              showColon={true}
            />
            <div className="flex flex-col">
              <p className="font-primaryBold text-textQuattuordenary text-minus1 sm:text-base break-words w-[180px] md:w-[210px]">
                {props.fields.CurrentPlanLabel?.value}
              </p>
              <div className="flex flex-col">
                <span className="font-primaryRegular text-textQuattuordenary  text-minus1">
                  {planDetails.KYCPlan.PRODUCT}
                </span>
                <span className="font-primaryRegular text-textQuattuordenary  text-minus1">
                  {planDetails.KYCPlan?.TERMMONTHCOUNT === 0
                    ? 'Month-to-Month'
                    : planDetails.KYCPlan.TERMMONTHCOUNT + ' Months - Fixed'}{' '}
                  | {KYCPlanPrice.toFixed(2) + '¢' + ' per kWh'}
                </span>
              </div>
            </div>
            <InfoText
              label={props.fields.CurrentServiceEndDateLabel?.value}
              value={transferDetails.transferInfo.serviceStopDate}
              isRow={false}
              showColon={true}
            />
          </div>
          <div className="w-full flex flex-col bg-bgQuinary h-fit md:flex-row gap-[24px]">
            <Text
              tag="p"
              className="font-primaryRegular text-textQuattuordenary text-minus1"
              field={props.fields.OrderInformationDescription}
            />
          </div>
          <div className="w-full flex flex-col bg-bgQuinary h-fit md:flex-row gap-[24px]">
            <Text
              tag="p"
              className="font-primaryRegular text-textQuattuordenary text-minus1 sm:text-base font-bold"
              field={props.fields.EffectiveNewPlanLabel}
            />
          </div>
          <hr className="border-t-1 border-borderVigintiternary border-solid w-full max-w-[800px] px-[15px]  border-[1px] block m-0 my-[20px]" />
          <div className="flex flex-row md:items-center">
            <Text
              tag="p"
              className="font-primaryBold text-textQuattuordenary text-plus1 md:flex-grow md:text-plus3 break-words max-w-[630px] pr-4"
              field={props.fields.OrderInformationTitle}
            />
          </div>
          <div className="flex flex-col gap-2 md:gap-3">
            <InfoText
              label={props.fields.CustomerNameLabel?.value}
              value={`${authUser.userFirstName} ${authUser.userLastName}`}
              isRow={false}
              showColon={true}
            />
            <InfoText
              label={props.fields.AccountNumberLabel?.value}
              value={planDetails.KYCPlan.CONTRACT_NO as string}
              isRow={false}
              showColon={true}
            />
          </div>
          <hr className="border-t-1 border-borderVigintiternary border-solid w-full max-w-[800px] px-[15px] border-[1px] block m-0 my-[20px]" />
        </div>
      </div>
      <Placeholder
        rendering={props.rendering}
        name="jss-addonplans"
        render={(components) => {
          return <div className="">{components}</div>;
        }}
      />
      {!isOrderCompleted && (
        <div className="sm:m-0 m-auto">
          <Button className="whitespace-normal leading-[18px]" onClick={orderAddonProducts}>
            {Object.keys(planDetails?.addonPlans).length > 0
              ? `${props.fields.OrderProductAndGoToAccountSummary?.value}`
              : `${props.fields.AccountSummaryButtonText?.value}`}
          </Button>
        </div>
      )}
    </div>
  );
};

export { TransferConfirmation };
const Component = withDatasourceCheck()<TransferConfirmationProps>(TransferConfirmation);
export default aiLogger(Component, Component.name);
