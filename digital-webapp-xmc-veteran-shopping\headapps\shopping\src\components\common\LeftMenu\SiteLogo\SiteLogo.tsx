import {
  ImageField,
  Link,
  <PERSON>Field,
  withDatasourceCheck,
  Image,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';

type SiteLogoProps = ComponentProps & {
  fields: {
    Logo: ImageField;
    LogoRedirectLink: LinkField;
  };
};

const SiteLogo = (props: SiteLogoProps): JSX.Element | null => {
  return (
    <div>
      {/* logo */}
      <div className="items-center md:my-6 pb-3 cursor-pointer sm:p-5 sm:bg-bgQuinary sm:mt-0">
        <Link className="w-full" title="Logo" field={props.fields.LogoRedirectLink}>
          <Image
            className="sm:max-w-[120px] w-full max-w-[140px] m-auto mt-2 sm:mt-0"
            field={props.fields.Logo}
          />
        </Link>
      </div>
    </div>
  );
};

export { SiteLogo };
const Component = withDatasourceCheck()<SiteLogoProps>(SiteLogo);
export default aiLogger(Component, Component.name);
