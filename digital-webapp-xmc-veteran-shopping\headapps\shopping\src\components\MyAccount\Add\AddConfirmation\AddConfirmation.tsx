import {
  Text,
  Field,
  LinkField,
  withData<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Placeholder,
  RichText,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import PrintPageButton from 'components/Elements/PrintPageButton/PrintPageButton';
import InfoText from 'components/common/InfoText/InfoText';
import { ComponentProps } from 'lib/component-props';
import { useRef, useState } from 'react';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import { useNavigationDirection } from 'src/hooks/useNavigationDirection';
import { useRouter } from 'next/router';
import Button from 'components/Elements/Button/Button';
import { DwellingType, QueryParamsMapType } from 'src/utils/query-params-mapping';
import {
  OrderAddonProductRequest,
  OrderAddonProductResponse,
  OrderedAddonProduct,
} from 'src/services/AddOnProductAPI/types';
import axios, { AxiosError, AxiosResponse } from 'axios-1.4';
import { setOrderedPlans } from 'src/stores/planSlice';
import { logErrorToAppInsights } from 'lib/app-insights-log-error';
import { useLoader } from 'src/hooks/modalhooks';
import PageBuilder from 'src/components/PageBuilder/PageBuilder';

type AddConfirmationProps = ComponentProps & {
  fields: {
    ConfirmationTitle: Field<string>;
    ConfirmationNumberLabel: Field<string>;
    AccountNumberLabel: Field<string>;
    PrintPageLabel: Field<string>;
    PersonalInformationTitle: Field<string>;
    PaperlessBillingLabel: Field<string>;
    SecondaryAccountHolderLabel: Field<string>;
    ServiceInformationTitle: Field<string>;
    BillingAddressLabel: Field<string>;
    ServiceAddressLabel: Field<string>;
    ServiceStartDateLabel: Field<string>;
    AccountSummaryButtonText: Field<string>;
    OrderProductAndGoToAccountSummary: Field<string>;
    MyAccountPageUrl: LinkField;
    EffectiveNewPlanLabel: Field<string>;
    FullNameLabel: Field<string>;
    NewPlanLabel: Field<string>;
    PlanGreenText: Field<string>;
    PaperlessBillingTitle: Field<string>;
  };
};

const AddConfirmation = (props: AddConfirmationProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  if (isPageEditing) return <PageBuilder componentName="Fields of AddConfirmation" />;
  let dispatch: ReturnType<typeof useAppDispatch>;
  let addHomeInfo = undefined;
  let planDetails = undefined;
  let authUser = undefined;
  if (!isPageEditing) {
    dispatch = useAppDispatch();
    addHomeInfo = useAppSelector((state) => state.add?.addHomeInfo);
    planDetails = useAppSelector((state) => state.plans);
    authUser = useAppSelector((state) => state.authuser);
  }
  useNavigationDirection();
  const printRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const { contractaccount, zip, esiid, planid, dwel } = router.query as QueryParamsMapType;
  const [isOrderCompleted, setIsOrderCompleted] = useState(false);
  const SelectedPlanRate = planDetails?.selectedPlan?.rate * 100;
  const { openModal } = useLoader();
  async function redirectToNextPage() {
    if (props.fields?.MyAccountPageUrl?.value?.href !== '') {
      openModal();
      const decodedUri = decodeURIComponent(props.fields.MyAccountPageUrl?.value.href as string);
      router.push({
        pathname: decodedUri,
        query: {
          clearcache: true,
        },
      });
    }
  }

  const orderAddonProducts = async () => {
    try {
      const addonPlanKeys = Object.keys(planDetails.addonPlans);
      if (addonPlanKeys && addonPlanKeys.length !== 0) {
        const selectedAddons: OrderedAddonProduct[] = addonPlanKeys.map((key) => {
          return {
            productId: planDetails.addonPlans[key].planId,
            quantity: 1,
          };
        });

        const orderAddonReq = await axios.post<
          OrderAddonProductResponse,
          AxiosResponse<OrderAddonProductResponse>,
          OrderAddonProductRequest
        >(
          '/api/myaccount/plans/addonplans',
          {
            businessPartnerNumber: authUser.bpNumber,
            contractAccountNumber: contractaccount,
            esiid: esiid,
            zipCode: zip,
            currentPlanId: planid,
            dwellingType: DwellingType[dwel as string],
            language: 'English',
            productOrders: selectedAddons,
          },
          { headers: { 'Content-Type': 'application/json' } }
        );
        setIsOrderCompleted(true);
        dispatch(setOrderedPlans(orderAddonReq.data.result));
        redirectToNextPage();
      }
      redirectToNextPage();
    } catch (err) {
      const error = err as AxiosError;
      logErrorToAppInsights(error, {
        componentStack: 'My Account Place Order Transfer - orderAddonProducts',
      });
      const queryParams = {
        errorCode: 'My Account Place Order Transfer - orderAddonProducts',
      };
      router.push({
        pathname: '/oops',
        query: { ...queryParams },
      });
    }
  };

  return (
    <div className="flex flex-col sm:px-0  gap-5 w-full md:w-full md:max-w-full wide:w-full ipad:w-full wide:px-[20px] ipad:px-[20px] ">
      <div className="enrollment-confirmation print:m-8 print:w-11/12 hidden" ref={printRef}>
        <div className="flex flex-col md:p-[34px] gap-5 md:gap-8 px-[20px]">
          <div className="flex flex-row md:items-center">
            <Text
              tag="p"
              className="font-primaryBold text-textPrimary md:flex-grow text-base md:text-plus2 break-words sm:max-w-[630px] sm:pr-4"
              field={props.fields.ConfirmationTitle}
            />
            <PrintPageButton label={props.fields.PrintPageLabel?.value} printRef={printRef} />
          </div>
          <div className="flex flex-col gap-2 md:gap-3">
            <InfoText
              label={props.fields.ConfirmationNumberLabel?.value}
              value={addHomeInfo?.serviceAccount}
              showColon={true}
            />
            <InfoText
              label={props.fields.AccountNumberLabel?.value}
              value={addHomeInfo?.contractAccount}
              showColon={true}
            />
          </div>

          <div className="w-full flex flex-col bg-white h-fit p-5 md:p-8 md:flex-row gap-[10px]">
            <div className="flex flex-col gap-2 md:w-1/2">
              <Text
                tag="p"
                className="font-primaryRegular text-textPrimary text-minus1 md:text-base"
                field={props.fields?.PaperlessBillingTitle}
              />
              {addHomeInfo?.secondaryAccountHolder.trim() && (
                <InfoText
                  label={props.fields.SecondaryAccountHolderLabel?.value}
                  value={addHomeInfo?.secondaryAccountHolder}
                  showColon={true}
                />
              )}
              <InfoText
                label={props.fields.PaperlessBillingLabel?.value}
                value={addHomeInfo?.paperlessBilling ? 'Yes' : 'No'}
                showColon={true}
              />
            </div>
            <div className="flex flex-col gap-2 md:w-1/2">
              <Text
                tag="p"
                className="font-primaryRegular text-textPrimary text-minus1 md:text-base"
                field={props.fields.ServiceInformationTitle}
              />
              {addHomeInfo?.billingAddress.trim() && (
                <InfoText
                  label={props.fields.BillingAddressLabel?.value}
                  value={addHomeInfo?.billingAddress}
                  showColon={true}
                />
              )}
              <InfoText
                label={props.fields.ServiceStartDateLabel.value}
                value={addHomeInfo?.startDate}
                showColon={true}
              />
              <InfoText
                label={props.fields.ServiceAddressLabel?.value}
                value={addHomeInfo?.serviceAddress}
                showColon={true}
              />
            </div>
          </div>
          {planDetails?.EVDisclaimer?.isEVSelected && (
            <div className="w-full flex flex-col bg-white h-fit p-5 sm:py-3 md:p-8 md:flex-row gap-[24px]">
              <RichText
                tag="p"
                className="font-primaryRegular text-textQuattuordenary text-minus1 sm:text-base"
                field={{ value: planDetails.EVDisclaimer.EVDisclaimerMessage }}
              />
            </div>
          )}
        </div>
      </div>

      <div className=" mt-[40px] sm:px-0 px-[20px]">
        <Text
          tag="p"
          className="font-primaryBold text-plus1 sm:text-plus3 text-textQuattuordenary"
          field={props.fields.ConfirmationTitle}
        />
        <Text
          tag="p"
          className="font-primaryRegular text-minus1 sm:text-base text-textQuattuordenary my-[20px]"
          field={props.fields.PlanGreenText}
        />
        <div className="flex my-[10px]">
          <RichText
            tag="p"
            className="font-primaryRegular text-minus1 sm:text-base text-textQuattuordenary"
            field={{
              value: props.fields.ConfirmationNumberLabel.value?.replace(
                '${ConfirmId}',
                `${' '} ${addHomeInfo?.serviceAccount}`
              ),
            }}
          />
        </div>
        <Text
          tag="p"
          className="font-primaryBold text-minus1 sm:text-base  text-textQuattuordenary my-[10px] "
          field={props.fields.ServiceAddressLabel}
        />
        <div className="font-primaryRegular text-minus1 sm:text-base  text-textQuattuordenary">
          {addHomeInfo?.billingAddress}
        </div>
        <Text
          tag="p"
          className="font-primaryBold text-minus1 sm:text-base text-textQuattuordenary mt-[15px] mb-[5px]"
          field={props.fields.NewPlanLabel}
        />
        <div className="flex flex-col">
          <span className="font-primaryRegular text-textQuattuordenary text-minus1 sm:text-base">
            {planDetails?.selectedPlan?.planName}
          </span>
          <span className="font-primaryRegular text-textQuattuordenary text-minus1 sm:text-base">
            {planDetails?.selectedPlan?.term === 0
              ? 'Month-to-Month'
              : planDetails?.selectedPlan?.term + ' Months - Fixed'}{' '}
            | {SelectedPlanRate.toFixed(2) + '¢' + ' per kWh'}
          </span>
        </div>
        <Text
          tag="p"
          className="font-primaryBold text-minus1 sm:text-base text-textQuattuordenary mt-[15px] mb-[5px]"
          field={props.fields.ServiceStartDateLabel}
        />
        <div className="font-primaryRegular text-minus1 sm:text-base text-textQuattuordenary">
          {addHomeInfo?.startDate}
        </div>
        <Text
          tag="p"
          className="font-primaryBold text-minus1 sm:text-base text-textQuattuordenary mt-[15px] mb-[5px]"
          field={props.fields.EffectiveNewPlanLabel}
        />
        <hr className="border-borderVigintiternary my-[30px]" />

        <Text
          tag="p"
          className="font-primaryBold text-plus sm:text-plus3 text-textQuattuordenary mt-[10px] mb-[15px]"
          field={props.fields.ServiceInformationTitle}
        />
        <Text
          tag="p"
          className="font-primaryBold text-minus1 sm:text-base text-textQuattuordenary mb-[5px]"
          field={props.fields.FullNameLabel}
        />
        <div className="font-primaryRegular text-minus1 sm:text-base text-textQuattuordenary mt-[5px]">
          {authUser?.userFirstName} {authUser?.userLastName}
        </div>

        <Text
          tag="p"
          className="font-primaryBold text-minus1 sm:text-base text-textQuattuordenary mt-[10px]"
          field={props.fields.AccountNumberLabel}
        />
        <div className="font-primaryRegular text-minus1 sm:text-base text-textQuattuordenary mt-[5px]">
          {addHomeInfo?.contractAccount}
        </div>

        <hr className="border-borderVigintiternary mt-[30px]" />
      </div>

      <Placeholder
        rendering={props.rendering}
        name="jss-addonplans"
        render={(components) => {
          return <div className="df">{components}</div>;
        }}
      />
      {!isOrderCompleted && (
        <div className="sm:m-0 m-auto w-full px-6 sm:px-0">
          <Button
            className="whitespace-normal leading-[18px] w-full sm:w-auto"
            onClick={orderAddonProducts}
          >
            {Object?.keys(planDetails?.addonPlans)?.length > 0
              ? `${props.fields.OrderProductAndGoToAccountSummary?.value}`
              : `${props.fields.AccountSummaryButtonText?.value}`}
          </Button>
        </div>
      )}
    </div>
  );
};

export { AddConfirmation };
const Component = withDatasourceCheck()<AddConfirmationProps>(AddConfirmation);
export default aiLogger(Component, Component.name);
