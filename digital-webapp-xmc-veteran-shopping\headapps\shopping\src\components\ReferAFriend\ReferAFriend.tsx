import {
  RichText,
  withData<PERSON>ur<PERSON><PERSON>he<PERSON>,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { ReferAFriendProps } from './componentprops/ComponentProps';
import Button from 'components/Elements/Button/Button';
import { referAfriend } from './functions/referralFunction';
import { useAppSelector } from 'src/stores/store';

const ReferAFriend = (props: ReferAFriendProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let enrollmentInfo = undefined;
  if (!isPageEditing) {
    enrollmentInfo = useAppSelector((state) => state.enrollment?.enrollmentInfo);
  }
  return (
    <div className="flex flex-col gap-5 w-full sm:max-w-[894px] sm:my-5 wide:w-full ipad:w-full ipad:px-[40px]">
      <hr className="h-px my-8 bg-charcoal-25 border w-full m-auto wide:max-w-full ipad:max-w-full" />
      <div className="font-primaryBold text-textQuattuordenary md:text-plus2">
        {props?.fields?.Title.value}
      </div>
      <div>
        <RichText
          className="font-primaryRegular text-textQuattuordenary text-minus2"
          field={props?.fields?.Description}
        />
      </div>
      <div className="font-primaryBold text-textQuattuordenary text-minus2">
        {props?.fields?.CopyMessageTitle.value}
      </div>
      <div className="">
        <Button
          type="button"
          className="bg-bgQuinary text-textPrimary border-borderPrimary w-full sm:w-auto"
          showLoader={false}
          onClick={() =>
            referAfriend({
              data:
                props?.fields?.ReferralLinkURL?.value +
                props?.fields?.ReferralLinkQueryString?.value,
              cNumber: enrollmentInfo?.contractAccountNumber,
            })
          }
        >
          {props.fields?.ReferralLinkText.value}
        </Button>
      </div>
    </div>
  );
};

export { ReferAFriend };
const Component = withDatasourceCheck()<ReferAFriendProps>(ReferAFriend);
export default aiLogger(Component, Component.name);
